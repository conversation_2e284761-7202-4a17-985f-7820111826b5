import os
import time
import threading
import logging
import zmq
from config_manager import ConfigManager


class LeoSubscriber:
    def __init__(self, zmq_address=None, topic=None, save_directory=None, config_path="config.ini"):
        """
        Leo ZeroMQ订阅端，接收来自Inference.py的文件数据并保存到指定路径

        :param zmq_address: ZeroMQ连接地址（可选，优先使用配置文件）
        :param topic: ZeroMQ订阅主题（可选，优先使用配置文件）
        :param save_directory: 文件保存目录（可选，优先使用配置文件）
        :param config_path: 配置文件路径（默认: config.ini）
        """
        # 加载配置：优先使用环境变量 ELECTRON_CONFIG
        env_cfg = os.environ.get('ELECTRON_CONFIG')
        cfg_path = env_cfg if env_cfg else config_path
        self.config_manager = ConfigManager(cfg_path)
        config = self.config_manager.get_all_config()

        # 参数优先级：直接传参 > 配置文件 > 默认值
        self.zmq_address = zmq_address or config.get('subscriber', {}).get('address', 'tcp://localhost:5557')
        # 支持多地址，使用分号或逗号分隔
        self.zmq_addresses = self._parse_addresses(self.zmq_address)
        self.topic = topic or config.get('subscriber', {}).get('topic', 'file_data')

        self.running = False
        self.context = None
        self.subscriber = None
        self.receive_thread = None

        # 配置日志
        log_config = config.get('logging', {})
        logging.basicConfig(
            level=log_config.get('level', 'INFO'),
            format=log_config.get('format', '%(asctime)s - %(name)s - %(levelname)s - %(message)s'),
            datefmt=log_config.get('date_format', '%Y-%m-%d %H:%M:%S')
        )
        self.logger = logging.getLogger('LeoSubscriber')
        self.logger.info(f"初始化Leo订阅端")
        self.logger.info(f"ZeroMQ连接地址: {self.zmq_addresses}")
        self.logger.info(f"订阅主题: {self.topic}")

    def _parse_addresses(self, addr_value):
        """将地址字符串/列表解析为地址列表。支持分号或逗号分隔。"""
        if addr_value is None:
            return []
        if isinstance(addr_value, (list, tuple, set)):
            return [str(a).strip() for a in addr_value if str(a).strip()]
        # 字符串：按分号或逗号拆分
        s = str(addr_value)
        parts = []
        for sep in [';', ',']:
            if sep in s:
                parts = [p.strip() for p in s.split(sep)]
                break
        if not parts:
            parts = [s.strip()]
        # 去除空项
        return [p for p in parts if p]

    def _initialize_zmq(self):
        """初始化ZeroMQ订阅者（支持多地址）"""
        try:
            self.context = zmq.Context()
            self.subscriber = self.context.socket(zmq.SUB)
            # 连接到所有配置的地址
            connected_any = False
            for addr in self.zmq_addresses:
                try:
                    self.subscriber.connect(addr)
                    self.logger.info(f"已连接到 ZeroMQ 地址: {addr}")
                    connected_any = True
                except zmq.ZMQError as ce:
                    self.logger.error(f"连接 ZeroMQ 地址失败: {addr}, 错误: {ce}")
            if not connected_any:
                raise RuntimeError("没有成功连接的ZeroMQ地址，请检查配置的subscriber.address")

            self.subscriber.setsockopt(zmq.SUBSCRIBE, self.topic.encode('utf-8'))
            self.logger.info(f"ZeroMQ订阅者已连接到 {self.zmq_addresses}，订阅主题: {self.topic}")
        except Exception as e:
            self.logger.error(f"ZeroMQ连接初始化失败: {e}")
            raise

    def _receive_messages(self):
        """接收ZeroMQ消息并处理文件数据"""
        self.logger.info("开始接收消息...")
        
        while self.running:
            try:
                # 非阻塞式接收消息
                message = self.subscriber.recv_multipart(zmq.NOBLOCK)
                self._process_message(message)
                
            except zmq.Again:
                # 没有消息可接收，等待一下
                time.sleep(0.1)
                continue
            except zmq.ZMQError as e:
                if self.running:  # 只有在运行状态下才记录错误
                    self.logger.error(f"ZeroMQ接收错误: {e}")
                break
            except Exception as e:
                self.logger.error(f"处理消息时发生错误: {e}")
                continue
        
        self.logger.info("停止接收消息")

    def _process_message(self, message):
        """处理接收到的消息"""
        try:
            # 按照Inference.py的发送格式解析: [topic, filename, file_size, file_data]
            if len(message) != 5:
                self.logger.warning(f"消息格式错误，期望4部分，实际收到{len(message)}部分")
                return
            
            topic = message[0].decode('utf-8')
            filename = message[1].decode('utf-8') 
            file_size = int(message[2].decode('utf-8'))
            file_abs_path = message[3]
            file_data = message[4]
            
            self.logger.info(f"收到文件: {filename}, 大小: {file_size} 字节, 主题: {topic}")
            
            # 验证文件大小
            if len(file_data) != file_size:
                self.logger.warning(f"文件大小不匹配，期望: {file_size}, 实际: {len(file_data)}")
            
            # 保存文件到绝对路径
            self._save_file_to_absolute_path(filename, file_abs_path, file_data)
            
        except Exception as e:
            self.logger.error(f"解析消息失败: {e}")

    def _save_file_to_absolute_path(self, filename, file_abs_path, file_data):
        """将文件数据保存到绝对路径"""
        try:
            
            
            
            # 写入文件
            with open(file_abs_path, 'wb') as f:
                f.write(file_data)
            
            self.logger.info(f"文件已保存到: {file_abs_path}")
            
        except Exception as e:
            self.logger.error(f"保存文件失败 {filename}: {e}")

    def start(self):
        """启动订阅服务"""
        if not self.running:
            try:
                # 初始化ZeroMQ
                self._initialize_zmq()
                
                self.running = True
                
                # 启动接收线程
                self.receive_thread = threading.Thread(target=self._receive_messages, daemon=True)
                self.receive_thread.start()
                
                self.logger.info("Leo订阅服务已启动")
                self.logger.info("按 Ctrl+C 停止服务...")
                
                # 主循环
                while self.running:
                    time.sleep(1)
                
            except KeyboardInterrupt:
                self.stop()
            except Exception as e:
                self.logger.error(f"启动过程中发生错误: {e}")
                self.stop()
    
    def stop(self):
        """停止订阅服务"""
        if self.running:
            self.running = False
            
            # 等待接收线程结束
            if self.receive_thread and self.receive_thread.is_alive():
                self.receive_thread.join(timeout=5)
            
            # 关闭ZeroMQ资源
            if self.subscriber:
                self.subscriber.close(linger=0)
            if self.context:
                self.context.term()
            
            self.logger.info("Leo订阅服务已停止")


def main():
    """主函数"""
    try:
        # 创建订阅者实例，使用配置文件
        subscriber = LeoSubscriber()

        # 启动订阅服务
        subscriber.start()

    except FileNotFoundError as e:
        print(f"错误: {e}")
        print("请确保 config.ini 配置文件存在于当前目录中")
    except Exception as e:
        print(f"启动失败: {e}")


if __name__ == "__main__":
    main()
