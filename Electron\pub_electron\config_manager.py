import os
import configparser
import logging
from typing import Dict, Any, Optional


class ConfigManager:
    """配置管理器，负责读取和管理 config.ini 配置文件"""
    
    def __init__(self, config_path: str = "config.ini"):
        """
        初始化配置管理器
        :param config_path: 配置文件路径，默认为 config.ini
        """
        self.config_path = config_path
        self.config = configparser.ConfigParser()
        self.logger = logging.getLogger('ConfigManager')
        
        # 加载配置文件
        self.load_config()
    
    def load_config(self) -> None:
        """加载配置文件"""
        if not os.path.exists(self.config_path):
            raise FileNotFoundError(f"配置文件未找到: {self.config_path}")
        
        try:
            self.config.read(self.config_path, encoding='utf-8')
            self.logger.info(f"配置文件加载成功: {self.config_path}")
        except Exception as e:
            raise RuntimeError(f"配置文件加载失败: {e}")
    
    def _get_boolean(self, section: str, key: str, default: bool = False) -> bool:
        """获取布尔值配置项"""
        try:
            return self.config.getboolean(section, key)
        except (configparser.NoSectionError, configparser.NoOptionError, ValueError):
            self.logger.warning(f"配置项 [{section}].{key} 未找到或格式错误，使用默认值: {default}")
            return default
    
    def _get_int(self, section: str, key: str, default: int = 0) -> int:
        """获取整数配置项"""
        try:
            return self.config.getint(section, key)
        except (configparser.NoSectionError, configparser.NoOptionError, ValueError):
            self.logger.warning(f"配置项 [{section}].{key} 未找到或格式错误，使用默认值: {default}")
            return default
    
    def _get_float(self, section: str, key: str, default: float = 0.0) -> float:
        """获取浮点数配置项"""
        try:
            return self.config.getfloat(section, key)
        except (configparser.NoSectionError, configparser.NoOptionError, ValueError):
            self.logger.warning(f"配置项 [{section}].{key} 未找到或格式错误，使用默认值: {default}")
            return default
    
    def _get_string(self, section: str, key: str, default: str = "") -> str:
        """获取字符串配置项"""
        try:
            return self.config.get(section, key)
        except (configparser.NoSectionError, configparser.NoOptionError):
            self.logger.warning(f"配置项 [{section}].{key} 未找到，使用默认值: {default}")
            return default
    
    def get_satellite_config(self) -> Dict[str, Any]:
        """获取卫星系统配置"""
        # 处理多目录配置
        watch_dirs_str = self._get_string('satellite', 'watch_dirs', './data')
        watch_dirs = [dir.strip() for dir in watch_dirs_str.split(';') if dir.strip()]
        
        # 处理源图像文件扩展名
        extensions_str = self._get_string('satellite', 'source_image_extensions', '.jpg,.jpeg,.png')
        source_extensions = [ext.strip().lower() for ext in extensions_str.split(',') if ext.strip()]
        
        return {
            'watch_dirs': watch_dirs,
            'recursive': self._get_boolean('satellite', 'recursive', False),
            'instance_name': self._get_string('satellite', 'instance_name', 'Leo1'),
            'source_image_extensions': source_extensions,
            'source_task_category': self._get_string('satellite', 'source_task_category', '0')
        }
    
    def get_zmq_config(self) -> Dict[str, Any]:
        """获取ZeroMQ配置"""
        return {
            'connect_address': self._get_string('zmq', 'connect_address', 'tcp://localhost:5556'),
            'topic': self._get_string('zmq', 'topic', 'cluster'),
            'linger_time': self._get_float('zmq', 'linger_time', 0.5)
        }
    
    def get_file_processing_config(self) -> Dict[str, Any]:
        """获取文件处理配置"""
        return {
            'max_attempts': self._get_int('file_processing', 'max_attempts', 5),
            'check_interval': self._get_float('file_processing', 'check_interval', 0.2),
            'keep_source_files': self._get_boolean('file_processing', 'keep_source_files', True)
        }
    
    def get_logging_config(self) -> Dict[str, Any]:
        """获取日志配置"""
        level_str = self._get_string('logging', 'level', 'INFO').upper()
        
        # 将字符串转换为logging级别
        level_map = {
            'DEBUG': logging.DEBUG,
            'INFO': logging.INFO,
            'WARNING': logging.WARNING,
            'ERROR': logging.ERROR,
            'CRITICAL': logging.CRITICAL
        }
        
        level = level_map.get(level_str, logging.INFO)
        
        return {
            'level': level,
            'format': self._get_string('logging', 'format', '[%(asctime)s] [%(name)s] %(message)s'),
            'date_format': self._get_string('logging', 'date_format', '%Y-%m-%d %H:%M:%S')
        }
    
    def get_subscriber_config(self) -> Dict[str, Any]:
        """获取订阅者配置"""
        return {
            'address': self._get_string('subscriber', 'address', 'tcp://localhost:5556'),
            'received_dir': self._get_string('subscriber', 'received_dir', 'received')
        }
    
    def get_all_config(self) -> Dict[str, Dict[str, Any]]:
        """获取所有配置"""
        return {
            'satellite': self.get_satellite_config(),
            'zmq': self.get_zmq_config(),
            'file_processing': self.get_file_processing_config(),
            'logging': self.get_logging_config(),
            'subscriber': self.get_subscriber_config()
        }
