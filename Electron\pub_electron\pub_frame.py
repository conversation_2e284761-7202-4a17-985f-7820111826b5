import os
import time
import threading
import logging
import zmq
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler
from config_manager import ConfigManager

class SatelliteFileHandler(FileSystemEventHandler):
    """处理文件系统事件的处理器"""
    def __init__(self, publisher):
        super().__init__()
        self.publisher = publisher
        self.logger = logging.getLogger('SatelliteFileHandler')
    
    def on_created(self, event):
        """当有新文件创建时调用"""
        if not event.is_directory:
            self.logger.info(f"检测到新文件: {event.src_path}")
            self.publisher.process_file(event.src_path)

class LowOrbitSatellite:
    def __init__(self, watch_dirs=None, zmq_address=None, topic=None, config_path="config.ini"):
        """
        低轨卫星文件监视与ZeroMQ发布类

        :param watch_dirs: 要监视的目录路径列表（可选，优先使用配置文件）
        :param zmq_address: ZeroMQ绑定地址（可选，优先使用配置文件）
        :param topic: ZeroMQ发布主题（可选，优先使用配置文件）
        :param config_path: 配置文件路径（默认: config.ini）
        """
        # 加载配置：优先使用环境变量 ELECTRON_CONFIG
        env_cfg = os.environ.get('ELECTRON_CONFIG')
        cfg_path = env_cfg if env_cfg else config_path
        self.config_manager = ConfigManager(cfg_path)
        config = self.config_manager.get_all_config()

        # 参数优先级：直接传参 > 配置文件 > 默认值
        if watch_dirs:
            self.watch_dirs = [os.path.abspath(d) for d in watch_dirs]
        else:
            self.watch_dirs = [os.path.abspath(d) for d in config['satellite']['watch_dirs']]
        
        self.recursive = config['satellite']['recursive']
        self.instance_name = config['satellite']['instance_name']
        self.source_image_extensions = config['satellite']['source_image_extensions']
        self.source_task_category = config['satellite']['source_task_category']

        # ZeroMQ配置
        self.zmq_address = zmq_address or config['zmq']['connect_address']
        self.topic = topic or config['zmq']['topic']

        # 文件处理配置
        self.max_attempts = config['file_processing']['max_attempts']
        self.check_interval = config['file_processing']['check_interval']
        self.keep_source_files = config['file_processing']['keep_source_files']
        self.linger_time = config['zmq']['linger_time']

        self.running = False
        self.observer = None

        # 设置ZeroMQ上下文和套接字
        self.context = zmq.Context()
        self.publisher = self.context.socket(zmq.PUB)

        # 确保监视目录存在
        for watch_dir in self.watch_dirs:
            os.makedirs(watch_dir, exist_ok=True)

        # 配置日志
        log_config = config['logging']
        logging.basicConfig(
            level=log_config['level'],
            format=log_config['format'],
            datefmt=log_config['date_format']
        )
        self.logger = logging.getLogger('LowOrbitSatellite')
        self.logger.info(f"初始化卫星系统，监视目录: {', '.join(self.watch_dirs)}")
        self.logger.info(f"ZeroMQ连接地址: {self.zmq_address}, 发布主题: {self.topic}")
        self.logger.info(f"递归监视: {self.recursive}, 最大尝试次数: {self.max_attempts}")
        self.logger.info(f"保留源文件: {self.keep_source_files}")
        self.logger.info(f"实例名称: {self.instance_name}")
        self.logger.info(f"源图像扩展名: {', '.join(self.source_image_extensions)}")
        self.logger.info(f"源任务类别标识: {self.source_task_category}")

    def _initialize_zmq(self):
        """初始化ZeroMQ发布者"""
        try:
            self.publisher.connect(self.zmq_address)
            self.logger.info(f"ZeroMQ发布者已连接到 {self.zmq_address}")
            # 给Frame一点时间来准备接收
            time.sleep(self.linger_time)
        except zmq.ZMQError as e:
            self.logger.error(f"ZeroMQ连接失败: {e}")
            raise

    def _handle_source_file(self, filepath: str) -> bool:
        """处理源文件：根据配置决定是否删除"""
        if self.keep_source_files:
            self.logger.debug(f"保留源文件: {os.path.basename(filepath)}")
            return True

        try:
            os.remove(filepath)
            self.logger.info(f"已删除源文件: {os.path.basename(filepath)}")
            return True
        except FileNotFoundError:
            self.logger.warning(f"源文件已不存在: {os.path.basename(filepath)}")
            return True  # 文件已不存在，视为成功
        except PermissionError:
            self.logger.error(f"权限不足，无法删除源文件: {os.path.basename(filepath)}")
            return False
        except OSError as e:
            self.logger.error(f"删除源文件失败: {os.path.basename(filepath)} - {e}")
            return False
        except Exception as e:
            self.logger.error(f"处理源文件时发生意外错误: {os.path.basename(filepath)} - {e}")
            return False

    def _is_source_image_file(self, filename: str) -> bool:
        """判断文件是否为源图像文件"""
        
        file_ext = os.path.splitext(filename)[1].lower()
        parts = filename.split('_')
        task_category = parts[1]
        if (file_ext in self.source_image_extensions) and (task_category == self.source_task_category) or ('merge' in filename):
            self.logger.debug(f"文件 {filename} 扩展名 {file_ext} 匹配源图像类型")
            return True
        
        return False
    
    def process_file(self, filepath):
        """处理新文件：读取内容并通过ZeroMQ发布"""
        filename = os.path.basename(filepath)
        abs_filepath = os.path.abspath(filepath)
        
        try:
            # 步骤1：获取文件后缀和文件名
            self.logger.info(f"处理文件: {filename}, 绝对路径: {abs_filepath}")
            
            # 步骤2：根据文件后缀和文件名判断是否为源图像
            if self._is_source_image_file(filename):
                self.logger.info(f"文件为源图像文件: {filename}，跳过处理")
                return True
            
            # 步骤3：非源图像文件，等待文件写入完成
            self.logger.info(f"非源图像文件: {filename}，等待文件写入完成...")
            prev_size = -1
            current_size = 0
            attempts = 0
            
            while attempts < self.max_attempts:
                if not os.path.exists(filepath):  # 文件可能已被删除
                    raise FileNotFoundError(f"文件已不存在: {filepath}")
                    
                current_size = os.path.getsize(filepath)
                if current_size == prev_size and current_size > 0:
                    break  # 文件大小稳定，可以读取

                prev_size = current_size
                time.sleep(self.check_interval)
                attempts += 1
            
            if current_size == 0:
                self.logger.warning(f"文件为空: {filename}")
                return False
                
            # 步骤4：发布完整文件内容（文件名、文件绝对路径、文件内容）
            with open(filepath, 'rb') as f:
                file_data = f.read()
            
            # 创建消息：[主题, 文件名, 文件大小, 文件绝对路径, 文件内容]
            message = [
                self.topic.encode('utf-8'),
                filename.encode('utf-8'),
                current_size,
                abs_filepath.encode('utf-8'),
                file_data
            ]
            
            # 发送多部分消息
            self.publisher.send_multipart(message)

            self.logger.info(f"已发布文件: {filename} ({len(file_data)}字节) 到主题 '{self.topic}' [路径: {abs_filepath}]")

            # 处理源文件（根据配置决定是否删除）
            if not self._handle_source_file(filepath):
                self.logger.warning(f"源文件处理失败，但数据已成功发送: {filename}")

            return True
            
        except FileNotFoundError:
            self.logger.warning(f"文件已消失: {filename}")
        except PermissionError:
            self.logger.error(f"权限不足，无法读取文件: {filename}")
        except OSError as e:
            self.logger.error(f"读取文件 {filename} 失败: {e}")
        except Exception as e:
            self.logger.error(f"处理文件时发生意外错误: {e}")
        
        return False
    
    def start(self):
        """启动监视服务"""
        if not self.running:
            try:
                # 初始化ZeroMQ
                self._initialize_zmq()
                
                # 设置文件系统观察者
                event_handler = SatelliteFileHandler(self)
                self.observer = Observer()
                
                # 为每个监视目录设置观察者
                for watch_dir in self.watch_dirs:
                    self.observer.schedule(event_handler, watch_dir, recursive=self.recursive)
                    self.logger.info(f"已设置监视目录: {watch_dir} (递归: {self.recursive})")
                
                # 启动观察者线程
                self.observer.start()
                self.running = True
                
                self.logger.info("卫星服务已启动")
                self.logger.info(f"正在监视 {len(self.watch_dirs)} 个目录")
                self.logger.info("按 Ctrl+C 停止服务...")
                
                # 主循环
                while self.running:
                    time.sleep(1)
                
            except KeyboardInterrupt:
                self.stop()
            except Exception as e:
                self.logger.error(f"启动过程中发生错误: {e}")
                self.stop()
    
    def stop(self):
        """停止监视服务"""
        if self.running:
            self.running = False
            
            # 停止文件观察者
            if self.observer:
                self.observer.stop()
                self.observer.join()
            
            # 关闭ZeroMQ资源
            self.publisher.close(linger=0)
            self.context.term()
            
            self.logger.info("卫星服务已停止")

# 订阅者示例，用于测试
def start_subscriber(zmq_address=None, topic=None, config_path="config.ini"):
    """启动一个测试订阅者来接收消息"""
    import threading
    import time

    # 加载配置
    config_manager = ConfigManager(config_path)
    config = config_manager.get_all_config()

    # 参数优先级：直接传参 > 配置文件
    zmq_address = zmq_address or config['subscriber']['address']
    topic = topic or config['zmq']['topic']
    received_dir = config['subscriber']['received_dir']

    context = zmq.Context()
    subscriber = context.socket(zmq.SUB)
    subscriber.connect(zmq_address)
    subscriber.setsockopt(zmq.SUBSCRIBE, topic.encode('utf-8'))

    logger = logging.getLogger('Subscriber')
    logger.info(f"订阅者已连接到 {zmq_address}, 主题: '{topic}'")
    
    def receive_messages():
        while True:
            try:
                # 接收多部分消息: [主题, 实例名称, 文件名, 文件绝对路径, 内容]
                message = subscriber.recv_multipart()
                if len(message) < 5:
                    logger.warning(f"消息格式错误，期望5部分，实际收到{len(message)}部分")
                    continue
                    
                recv_topic = message[0].decode('utf-8')
                instance_name = message[1].decode('utf-8')
                filename = message[2].decode('utf-8')
                file_abs_path = message[3].decode('utf-8')
                content = message[4]
                
                # 模拟处理数据
                logger.info(f"收到文件: {filename} ({len(content)}字节)")
                logger.info(f"来源实例: {instance_name}, 主题: {recv_topic}")
                logger.info(f"原始路径: {file_abs_path}")
                logger.info(f"文件前50字节: {content[:50].decode('utf-8', errors='replace')}...")
                
                # 保存接收到的文件（可选）
                save_path = os.path.join(received_dir, f"{instance_name}_{filename}")
                os.makedirs(received_dir, exist_ok=True)
                with open(save_path, 'wb') as f:
                    f.write(content)
                logger.info(f"文件已保存到: {save_path}")
                
            except zmq.ZMQError:
                break
            except Exception as e:
                logger.error(f"处理消息时出错: {e}")
    
    # 启动接收线程
    threading.Thread(target=receive_messages, daemon=True).start()

if __name__ == "__main__":
    try:
        # 创建卫星实例，使用配置文件
        satellite = LowOrbitSatellite()

        # 启动卫星服务
        satellite.start()

    except FileNotFoundError as e:
        print(f"错误: {e}")
        print("请确保 config.ini 配置文件存在于当前目录中")
    except Exception as e:
        print(f"启动失败: {e}")