import os
import configparser
import logging
from typing import Dict, Any, List


class ConfigManager:
    """Frame系统配置管理器，负责读取和管理 config.ini 配置文件"""
    
    def __init__(self, config_path: str = "config.ini"):
        """
        初始化配置管理器
        
        :param config_path: 配置文件路径，默认为 config.ini
        """
        self.config_path = config_path
        self.config = configparser.ConfigParser()
        self.logger = logging.getLogger('FrameConfigManager')
        
        # 加载配置文件
        self.load_config()
    
    def load_config(self) -> None:
        """加载配置文件"""
        if not os.path.exists(self.config_path):
            raise FileNotFoundError(f"配置文件未找到: {self.config_path}")
        
        try:
            self.config.read(self.config_path, encoding='utf-8')
            self.logger.info(f"配置文件加载成功: {self.config_path}")
        except Exception as e:
            raise RuntimeError(f"配置文件加载失败: {e}")
    
    def _get_boolean(self, section: str, key: str, default: bool = False) -> bool:
        """获取布尔值配置项"""
        try:
            return self.config.getboolean(section, key)
        except (configparser.NoSectionError, configparser.NoOptionError, ValueError):
            self.logger.warning(f"配置项 [{section}].{key} 未找到或格式错误，使用默认值: {default}")
            return default
    
    def _get_int(self, section: str, key: str, default: int = 0) -> int:
        """获取整数配置项"""
        try:
            return self.config.getint(section, key)
        except (configparser.NoSectionError, configparser.NoOptionError, ValueError):
            self.logger.warning(f"配置项 [{section}].{key} 未找到或格式错误，使用默认值: {default}")
            return default
    
    def _get_float(self, section: str, key: str, default: float = 0.0) -> float:
        """获取浮点数配置项"""
        try:
            return self.config.getfloat(section, key)
        except (configparser.NoSectionError, configparser.NoOptionError, ValueError):
            self.logger.warning(f"配置项 [{section}].{key} 未找到或格式错误，使用默认值: {default}")
            return default
    
    def _get_string(self, section: str, key: str, default: str = "") -> str:
        """获取字符串配置项"""
        try:
            return self.config.get(section, key)
        except (configparser.NoSectionError, configparser.NoOptionError):
            self.logger.warning(f"配置项 [{section}].{key} 未找到，使用默认值: {default}")
            return default
    
    def _get_list(self, section: str, key: str, default: List[str] = None) -> List[str]:
        """获取列表配置项（逗号分隔）"""
        if default is None:
            default = []
        
        try:
            value = self.config.get(section, key)
            # 分割字符串并去除空白
            return [item.strip() for item in value.split(',') if item.strip()]
        except (configparser.NoSectionError, configparser.NoOptionError):
            self.logger.warning(f"配置项 [{section}].{key} 未找到，使用默认值: {default}")
            return default
    
    def get_frame_config(self) -> Dict[str, Any]:
        """获取Frame系统配置"""
        return {
            'watch_dirs': self._get_string('frame', 'watch_dirs', './watch_dir'),
            'naming_strategy': self._get_string('frame', 'naming_strategy', 'timestamp'),
            'duplicate_handling': self._get_string('frame', 'duplicate_handling', 'rename'),
            'create_date_dirs': self._get_boolean('frame', 'create_date_dirs', True),
            'database_path': self._get_string('frame', 'database_path', 'task_data.db'),
            'enable_task_data_parsing': self._get_boolean('frame', 'enable_task_data_parsing', True),
            'custom_name_template': self._get_string('frame', 'custom_name_template', '{timestamp}_{original_name}'),
            'insert_sample_data': self._get_boolean('frame', 'insert_sample_data', False)
        }
    
    def get_logging_config(self) -> Dict[str, Any]:
        """获取日志配置"""
        level_str = self._get_string('logging', 'level', 'INFO').upper()
        
        # 将字符串转换为logging级别
        level_map = {
            'DEBUG': logging.DEBUG,
            'INFO': logging.INFO,
            'WARNING': logging.WARNING,
            'ERROR': logging.ERROR,
            'CRITICAL': logging.CRITICAL
        }
        
        level = level_map.get(level_str, logging.INFO)
        
        return {
            'level': level,
            'format': self._get_string('logging', 'format', '[%(asctime)s] [%(name)s] %(message)s'),
            'date_format': self._get_string('logging', 'date_format', '%Y-%m-%d %H:%M:%S')
        }

    def get_api_config(self) -> Dict[str, Any]:
        """获取API服务配置"""
        return {
            'enable_api': self._get_boolean('api', 'enable_api', True),
            'host': self._get_string('api', 'host', '0.0.0.0'),
            'port': self._get_int('api', 'port', 8000),
            'title': self._get_string('api', 'title', 'Frame Task Data API'),
            'description': self._get_string('api', 'description', '提供任务数据的RESTful API接口'),
            'version': self._get_string('api', 'version', '1.0.0'),
            'enable_docs': self._get_boolean('api', 'enable_docs', True),
            'cors_origins': self._get_string('api', 'cors_origins', '*').split(','),
            'cors_credentials': self._get_boolean('api', 'cors_credentials', True),
            'cors_methods': self._get_string('api', 'cors_methods', 'GET,POST,PUT,DELETE,OPTIONS').split(','),
            'cors_headers': self._get_string('api', 'cors_headers', '*').split(',')
        }

    def get_zmq_config(self) -> Dict[str, Any]:
        """获取ZeroMQ配置"""
        return {
            'enable_zmq': self._get_boolean('zmq', 'enable_zmq', True),
            'bind_address': self._get_string('zmq', 'bind_address', 'tcp://*:5557'),
            'topic': self._get_string('zmq', 'topic', 'file_data'),
            'enable_directory_watch': self._get_boolean('zmq', 'enable_directory_watch', True),
            'result_output_dir': self._get_string('zmq', 'result_output_dir', './zmq_result_data')
        }

    def get_all_config(self) -> Dict[str, Dict[str, Any]]:
        """获取所有配置"""
        return {
            'frame': self.get_frame_config(),
            'logging': self.get_logging_config(),
            'api': self.get_api_config(),
            'zmq': self.get_zmq_config()
        }
    
    def validate_config(self) -> bool:
        """验证配置的有效性"""
        try:
            frame_config = self.get_frame_config()
            
            # 验证命名策略
            valid_strategies = ['original', 'timestamp', 'custom']
            if frame_config['naming_strategy'] not in valid_strategies:
                self.logger.error(f"无效的命名策略: {frame_config['naming_strategy']}")
                return False
            
            # 验证重复文件处理策略
            valid_handling = ['overwrite', 'rename', 'skip']
            if frame_config['duplicate_handling'] not in valid_handling:
                self.logger.error(f"无效的重复文件处理策略: {frame_config['duplicate_handling']}")
                return False
                        
            self.logger.info("配置验证通过")
            return True
            
        except Exception as e:
            self.logger.error(f"配置验证失败: {e}")
            return False
