import os
import time
import threading
import logging
import zmq
from dateutil import parser
import socket
import struct
import json
import base64
import sqlite3
import hashlib
import queue
import numpy as np
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any, Tuple
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler
from config_manager import ConfigManager


class DatabaseManager:
    """数据库管理类，负责SQLite数据库的创建、连接和数据操作"""

    def __init__(self, db_path: str = "task_data.db", logger: Optional[logging.Logger] = None, 
                 insert_sample_data: bool = False):
        """
        初始化数据库管理器

        :param db_path: 数据库文件路径
        :param logger: 日志记录器
        :param insert_sample_data: 是否插入示例数据
        """
        self.db_path = db_path
        self.logger = logger or logging.getLogger('DatabaseManager')
        self.connection = None
        self.insert_sample_data = insert_sample_data

        # 确保数据库目录存在
        db_dir = os.path.dirname(os.path.abspath(db_path))
        if db_dir:
            os.makedirs(db_dir, exist_ok=True)

        # 初始化数据库
        self._init_database()

        self.logger.info(f"数据库管理器初始化完成: {self.db_path}")

    def _init_database(self):
        """初始化数据库，创建表结构"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # 创建源数据表，保存数据的输入信息-插入操作
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS origional_table (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        sat_id TEXT,
                        sat_category TEXT,
                        sat_load_category TEXT,
                        planed_start_time TEXT,
                        planed_end_time TEXT,
                        image_size REAL,
                        image_name TEXT,
                        image_path TEXT,
                        image_truth_width REAL,
                        image_resolution REAL,
                        timestamp TEXT NOT NULL,
                        enu_base_longitude REAL,
                        enu_base_latitude REAL,
                        enu_base_altitude REAL,
                        sat_enu_x REAL,
                        sat_enu_y REAL,
                        sat_enu_z REAL,
                        cam_enu_x REAL,
                        cam_enu_y REAL,
                        cam_enu_z REAL,
                        cam_enu_w REAL,
                        sat_j2000_x REAL,
                        sat_j2000_y REAL,
                        sat_j2000_z REAL,
                        sat_qbi_x REAL,
                        sat_qbi_y REAL,
                        sat_qbi_z REAL,
                        sat_qbi_w REAL,
                        load_fov_x REAL,
                        load_fov_y REAL,
                        image_pixel_x REAL,
                        image_pixel_y REAL                    
                    )
                ''')

                # 创建真值表-插入操作
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS truth_table (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        sat_id TEXT,
                        timestamp TEXT NOT NULL,
                        target_id TEXT,
                        fleet_number TEXT,
                        target_category TEXT,
                        target_direction TEXT,
                        target_longitude REAL,
                        target_latitude REAL,
                        target_altitude REAL,
                        target_sat_z REAL,
                        target_sat_y REAL,
                        target_sat_x REAL,
                        target_draw_x REAL,
                        target_draw_y REAL,
                        target_draw_width REAL,
                        target_draw_height REAL,
                        target_return_x REAL,
                        target_return_y REAL,
                        target_return_width REAL,
                        target_return_height REAL
                    )
                ''')

                # 创建特征表，保存算法的中间结果信息--插入+更新操作
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS feature_table (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        sat_id TEXT NOT NULL,
                        fleet_number TEXT,
                        target_category TEXT,
                        target_id TEXT,
                        timestamp TEXT NOT NULL,
                        target_image_path TEXT,
                        target_truth BOOL,
                        target_x REAL,
                        target_y REAL,
                        target_width REAL,
                        target_height REAL,
                        target_longitude REAL,
                        target_latitude REAL,
                        target_fusion_longitude REAL,
                        target_fusion_latitude REAL,
                        target_longitude_speed REAL,
                        target_latitude_speed REAL,
                        target_total_speed REAL,
                        target_npy_path TEXT
                    )
                ''')
                

                # 创建决策表，保存数据的决策信息--只有插入操作
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS decision_table (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        timestamp TEXT,
                        fleet_number TEXT,
                        speed_threat REAL,
                        heading_threat REAL,
                        distance_threat REAL,
                        heading_angle REAL,
                        operate_begin TEXT,
                        operate_end TEXT,
                        fleet_prediction TEXT
                    )
                ''')

                # 创建索引以提高查询性能
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_origional_timestamp ON origional_table(timestamp)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_origional_sat_id ON origional_table(sat_id)')

                cursor.execute('CREATE INDEX IF NOT EXISTS idx_truth_sat_id ON truth_table(sat_id)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_truth_timestamp ON truth_table(timestamp)')

                # 最终提交
                conn.commit()
                self.logger.info("数据库特征表创建完成")

        except Exception as e:
            self.logger.error(f"初始化数据库失败: {e}")
            raise

    def get_connection(self) -> sqlite3.Connection:
        """获取数据库连接"""
        try:
            if self.connection is None or self.connection.execute('SELECT 1').fetchone() is None:
                self.connection = sqlite3.connect(self.db_path, check_same_thread=False)
                self.connection.row_factory = sqlite3.Row  # 使结果可以按列名访问
            return self.connection
        except Exception as e:
            self.logger.error(f"获取数据库连接失败: {e}")
            raise

    def close_connection(self):
        """关闭数据库连接"""
        if self.connection:
            try:
                self.connection.close()
                self.connection = None
                self.logger.debug("数据库连接已关闭")
            except Exception as e:
                self.logger.error(f"关闭数据库连接失败: {e}")

    def insert_origin_data(self, data: Dict[str, Any]) -> Optional[int]:
        """插入源数据 - 优化版，支持任意参数组合
        
        :param data: 源数据字典，可包含任意origional_table的字段
        :return: 插入记录的ID，失败返回None
        """
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            
            # 定义origional_table的所有列（除了自增主键），匹配实际表结构
            all_columns = {
                'sat_id': 'TEXT',
                'sat_category': 'TEXT',
                'sat_load_category': 'TEXT',
                'planed_start_time': 'TEXT',
                'planed_end_time': 'TEXT',
                'image_size': 'REAL',
                'image_name': 'TEXT',
                'image_path': 'TEXT',
                'image_truth_width': 'REAL',
                'image_resolution': 'REAL',
                'timestamp': 'TEXT NOT NULL',
                'enu_base_longitude': 'REAL',
                'enu_base_latitude': 'REAL',
                'enu_base_altitude': 'REAL',
                'sat_enu_x': 'REAL',
                'sat_enu_y': 'REAL',
                'sat_enu_z': 'REAL',
                'cam_enu_x': 'REAL',
                'cam_enu_y': 'REAL',
                'cam_enu_z': 'REAL',
                'cam_enu_w': 'REAL',
                'sat_j2000_x': 'REAL',
                'sat_j2000_y': 'REAL',
                'sat_j2000_z': 'REAL',
                'sat_qbi_x': 'REAL',
                'sat_qbi_y': 'REAL',
                'sat_qbi_z': 'REAL',
                'sat_qbi_w': 'REAL',
                'load_fov_x': 'REAL',
                'load_fov_y': 'REAL',
                'image_pixel_x': 'REAL',
                'image_pixel_y': 'REAL'
            }
            
            # 检查必须字段
            required_fields = ['sat_id', 'sat_category', 'timestamp']
            missing_required = [field for field in required_fields if not data.get(field)]
            if missing_required:
                raise ValueError(f"缺少必须字段: {missing_required}")
            
            # 只包含数据中存在且在表结构中的列
            columns_to_insert = []
            values_to_insert = []
            
            for column, value in data.items():
                if column in all_columns and value is not None:
                    columns_to_insert.append(column)
                    # 处理特殊类型
                    if 'INTEGER' in all_columns[column]:
                        # 整型处理
                        try:
                            values_to_insert.append(int(float(str(value))) if value != '' else None)
                        except (ValueError, TypeError):
                            values_to_insert.append(None)
                    elif 'REAL' in all_columns[column]:
                        # 浮点型处理
                        try:
                            values_to_insert.append(float(value) if value != '' else None)
                        except (ValueError, TypeError):
                            values_to_insert.append(None)
                    else:
                        # 文本类型处理
                        values_to_insert.append(str(value) if value != '' else None)
            
            # 检查是否有数据要插入
            if not columns_to_insert:
                raise ValueError("没有有效的数据列可以插入")
            
            # 构建动态SQL语句
            placeholders = ', '.join(['?'] * len(columns_to_insert))
            insert_sql = f"""
                INSERT INTO origional_table ({', '.join(columns_to_insert)})
                VALUES ({placeholders})
            """
            
            # 记录调试信息
            self.logger.debug(f"插入源数据: {len(columns_to_insert)}个字段 - {columns_to_insert}")
            
            cursor.execute(insert_sql, values_to_insert)
            conn.commit()
            
            record_id = cursor.lastrowid
            self.logger.debug(f"成功插入源数据，记录ID: {record_id}")
            return record_id
            
        except Exception as e:
            self.logger.error(f"插入源数据失败: {e}")
            # 记录输入数据以便调试
            self.logger.debug(f"失败的输入数据: {data}")
            return None

    def insert_truth_data(self, data: Dict[str, Any]) -> Optional[int]:
        """插入真值数据 - 支持任意参数组合
        
        :param data: 真值数据字典，可包含任意truth_table的字段
        :return: 插入记录的ID，失败返回None
        """
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            
            # 定义truth_table的所有列（除了自增主键），匹配实际表结构
            all_columns = {
                'sat_id': 'TEXT',
                'timestamp': 'TEXT NOT NULL',
                'target_id': 'TEXT',
                'fleet_number': 'TEXT',
                'target_category': 'TEXT',
                'target_direction': 'TEXT',
                'target_longitude': 'REAL',
                'target_latitude': 'REAL',
                'target_altitude': 'REAL',
                'target_sat_z': 'REAL',
                'target_sat_y': 'REAL',
                'target_sat_x': 'REAL',
                'target_draw_x': 'REAL',
                'target_draw_y': 'REAL',
                'target_draw_width': 'REAL',
                'target_draw_height': 'REAL',
                'target_return_x': 'REAL',
                'target_return_y': 'REAL',
                'target_return_width': 'REAL',
                'target_return_height': 'REAL'
            }
            
            # 检查必须字段
            required_fields = ['sat_id', 'timestamp']
            missing_required = [field for field in required_fields if not data.get(field)]
            if missing_required:
                raise ValueError(f"缺少必须字段: {missing_required}")
            
            # 只包含数据中存在且在表结构中的列
            columns_to_insert = []
            values_to_insert = []
            
            for column, value in data.items():
                if column in all_columns and value is not None:
                    columns_to_insert.append(column)
                    # 处理特殊类型
                    if 'REAL' in all_columns[column]:
                        # 浮点型处理
                        try:
                            values_to_insert.append(float(value) if value != '' else None)
                        except (ValueError, TypeError):
                            values_to_insert.append(None)
                    else:
                        # 文本类型处理
                        values_to_insert.append(str(value) if value != '' else None)
            
            # 检查是否有数据要插入
            if not columns_to_insert:
                raise ValueError("没有有效的数据列可以插入")
            
            # 构建动态SQL语句
            placeholders = ', '.join(['?'] * len(columns_to_insert))
            insert_sql = f"""
                INSERT INTO truth_table ({', '.join(columns_to_insert)})
                VALUES ({placeholders})
            """
            
            # 记录调试信息
            self.logger.debug(f"插入真值数据: {len(columns_to_insert)}个字段 - {columns_to_insert}")
            
            cursor.execute(insert_sql, values_to_insert)
            conn.commit()
            
            record_id = cursor.lastrowid
            self.logger.debug(f"成功插入真值数据，记录ID: {record_id}")
            return record_id
            
        except Exception as e:
            self.logger.error(f"插入真值数据失败: {e}")
            # 记录输入数据以便调试
            self.logger.debug(f"失败的输入数据: {data}")
            return None

    def insert_feature_data(self, data: Dict[str, Any]) -> Optional[int]:
        """插入特征数据 - 优化版，支持任意参数组合
        
        :param data: 特征数据字典，可包含任意feature_table的字段
        :return: 插入记录的ID，失败返回None
        """
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            
            # 定义feature_table的所有列（除了自增主键）
            all_columns = {
                'sat_id': 'TEXT NOT NULL',
                'fleet_number': 'TEXT',
                'target_category': 'TEXT',
                'target_id': 'TEXT',
                'timestamp': 'TEXT NOT NULL',
                'target_image_path': 'TEXT',
                'target_truth': 'BOOL',
                'target_x': 'REAL',
                'target_y': 'REAL',
                'target_width': 'REAL',
                'target_height': 'REAL',
                'target_longitude': 'REAL',
                'target_latitude': 'REAL',
                'target_fusion_longitude': 'REAL',
                'target_fusion_latitude': 'REAL',
                'target_longitude_speed': 'REAL',
                'target_latitude_speed': 'REAL',
                'target_total_speed': 'REAL',
                'target_npy_path': 'TEXT'
            }
            
            # 检查必须字段
            required_fields = ['sat_id', 'timestamp']
            missing_required = [field for field in required_fields if not data.get(field)]
            if missing_required:
                raise ValueError(f"缺少必须字段: {missing_required}")
            
            # 只包含数据中存在且在表结构中的列
            columns_to_insert = []
            values_to_insert = []
            
            for column, value in data.items():
                if column in all_columns and value is not None:
                    columns_to_insert.append(column)
                    # 处理特殊类型
                    if all_columns[column] == 'BOOL':
                        # 布尔值处理
                        if isinstance(value, bool):
                            values_to_insert.append(value)
                        elif isinstance(value, str):
                            values_to_insert.append(value.lower() in ['true', '1', 'yes', 'real'])
                        else:
                            values_to_insert.append(bool(value))
                    elif 'INTEGER' in all_columns[column]:
                        # 整型处理
                        try:
                            values_to_insert.append(int(float(str(value))) if value != '' else None)
                        except (ValueError, TypeError):
                            values_to_insert.append(None)
                    elif 'REAL' in all_columns[column]:
                        # 浮点型处理
                        try:
                            values_to_insert.append(float(value) if value != '' else None)
                        except (ValueError, TypeError):
                            values_to_insert.append(None)
                    else:
                        # 文本类型处理
                        values_to_insert.append(str(value) if value != '' else None)
            
            # 检查是否有数据要插入
            if not columns_to_insert:
                raise ValueError("没有有效的数据列可以插入")
            
            # 构建动态SQL语句
            placeholders = ', '.join(['?'] * len(columns_to_insert))
            insert_sql = f"""
                INSERT INTO feature_table ({', '.join(columns_to_insert)})
                VALUES ({placeholders})
            """
            
            # 记录调试信息
            self.logger.debug(f"插入特征数据: {len(columns_to_insert)}个字段 - {columns_to_insert}")
            
            cursor.execute(insert_sql, values_to_insert)
            conn.commit()
            
            record_id = cursor.lastrowid
            self.logger.debug(f"成功插入特征数据，记录ID: {record_id}")
            return record_id
            
        except Exception as e:
            self.logger.error(f"插入特征数据失败: {e}")
            # 记录输入数据以便调试
            self.logger.debug(f"失败的输入数据: {data}")
            return None

    def insert_decision_data(self, data: Dict[str, Any]) -> Optional[int]:
        """插入决策数据 - 优化版，支持任意参数组合
        
        :param data: 决策数据字典，可包含任意decision_table的字段
        :return: 插入记录的ID，失败返回None
        """
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            
            # 定义decision_table的所有列（除了自增主键）
            all_columns = {
                'timestamp': 'TEXT',
                'fleet_number': 'TEXT',
                'speed_threat': 'REAL',
                'heading_threat': 'REAL',
                'distance_threat': 'REAL',
                'heading_angle': 'REAL',
                'operate_begin': 'TEXT',
                'operate_end': 'TEXT',
                'fleet_prediction': 'TEXT'
            }
            
            # 检查必须字段
            required_fields = ['operate_begin', 'operate_end']
            missing_required = [field for field in required_fields if not data.get(field)]
            if missing_required:
                raise ValueError(f"缺少必须字段: {missing_required}")
            
            # 只包含数据中存在且在表结构中的列
            columns_to_insert = []
            values_to_insert = []
            
            for column, value in data.items():
                if column in all_columns and value is not None:
                    columns_to_insert.append(column)
                    # 处理特殊类型
                    if 'INTEGER' in all_columns[column]:
                        # 整型处理
                        try:
                            values_to_insert.append(int(float(str(value))) if value != '' else None)
                        except (ValueError, TypeError):
                            values_to_insert.append(None)
                    elif 'REAL' in all_columns[column]:
                        # 浮点型处理
                        try:
                            values_to_insert.append(float(value) if value != '' else None)
                        except (ValueError, TypeError):
                            values_to_insert.append(None)
                    else:
                        # 文本类型处理
                        values_to_insert.append(str(value) if value != '' else None)
            
            # 检查是否有数据要插入
            if not columns_to_insert:
                raise ValueError("没有有效的数据列可以插入")
            
            # 构建动态SQL语句
            placeholders = ', '.join(['?'] * len(columns_to_insert))
            insert_sql = f"""
                INSERT INTO decision_table ({', '.join(columns_to_insert)})
                VALUES ({placeholders})
            """
            
            # 记录调试信息
            self.logger.debug(f"插入决策数据: {len(columns_to_insert)}个字段 - {columns_to_insert}")
            
            cursor.execute(insert_sql, values_to_insert)
            conn.commit()
            
            record_id = cursor.lastrowid
            self.logger.debug(f"成功插入决策数据，记录ID: {record_id}")
            return record_id
            
        except Exception as e:
            self.logger.error(f"插入决策数据失败: {e}")
            # 记录输入数据以便调试
            self.logger.debug(f"失败的输入数据: {data}")
            return None

class FileEventHandler(FileSystemEventHandler):
    """自定义文件系统事件处理器"""
    
    def __init__(self, frame_receiver, logger):
        """
        初始化事件处理器
        
        :param frame_receiver: FrameReceiver 实例
        :param logger: 日志记录器
        """
        super().__init__()
        self.frame_receiver = frame_receiver
        self.logger = logger
        self.file_extensions = ['.jpg', '.json']  # 监控的文件扩展名
    
    def on_created(self, event):
        """处理文件创建事件"""
        if not event.is_directory:
            file_path = event.src_path
            file_ext = os.path.splitext(file_path)[1].lower()
            
            if file_ext in self.file_extensions:
                self.logger.info(f"检测到新文件: {file_path}")
                self.frame_receiver.process_file(file_path)
    
    def on_modified(self, event):
        """处理文件修改事件"""
        if not event.is_directory:
            file_path = event.src_path
            file_ext = os.path.splitext(file_path)[1].lower()
            
            if file_ext in self.file_extensions:
                self.logger.info(f"检测到文件修改: {file_path}")
                self.frame_receiver.process_file(file_path)

class FrameReceiver:
    """Frame数据接收器，负责监听多个目录并处理文件"""
    
    def __init__(self, config_path: str = "config.ini"):
        """
        初始化Frame接收器
        
        :param config_path: 配置文件路径，默认为 config.ini
        """
        # Load config and validate
        # 允许使用环境变量 GEO_CONFIG 指定统一配置文件路径
        env_config_path = os.environ.get('GEO_CONFIG')
        effective_config_path = env_config_path if env_config_path else config_path
        self.config_manager = ConfigManager(effective_config_path)
        if not self.config_manager.validate_config():
            raise RuntimeError("配置文件验证失败")
        
        config = self.config_manager.get_all_config()
        
        # Frame配置
        frame_config = config['frame']
        
        # 处理多个监控目录
        watch_dirs = frame_config['watch_dirs']
        if isinstance(watch_dirs, str):
            self.watch_dirs = [d.strip() for d in watch_dirs.split(';') if d.strip()]
        elif isinstance(watch_dirs, list):
            self.watch_dirs = watch_dirs
        else:
            self.watch_dirs = []
        
        # 确保所有监控目录都是绝对路径
        self.watch_dirs = [os.path.abspath(d) for d in self.watch_dirs]
        
        self.naming_strategy = frame_config['naming_strategy']
        self.duplicate_handling = frame_config['duplicate_handling']
        self.create_date_dirs = frame_config['create_date_dirs']
        self.custom_name_template = frame_config['custom_name_template']
        self.database_path = frame_config['database_path']
        self.enable_task_data_parsing = frame_config['enable_task_data_parsing']
        self.insert_sample_data = frame_config['insert_sample_data']

        # 运行状态
        self.running = False

        # 配置日志
        log_config = config['logging']
        logging.basicConfig(
            level=log_config['level'],
            format=log_config['format'],
            datefmt=log_config['date_format']
        )
        self.logger = logging.getLogger('FrameReceiver')

        # Watchdog相关
        self.observers = []  # 存储多个观察者
        self.event_handler = FileEventHandler(self, self.logger)

        # 数据库管理器
        self.db_manager = None
        if self.enable_task_data_parsing:
            db_path = self.database_path
            insert_sample_data = self.insert_sample_data
            self.db_manager = DatabaseManager(db_path, self.logger, insert_sample_data)

        self.logger.info(f"初始化Frame接收器")
        self.logger.info(f"监控目录: {', '.join(self.watch_dirs)}")
        self.logger.info(f"命名策略: {self.naming_strategy}")
    
    def _generate_filename(self, original_name: str) -> str:
        """根据命名策略生成文件名"""
        if self.naming_strategy == 'original':
            return original_name
        
        elif self.naming_strategy == 'timestamp':
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S_%f')[:-3]  # 毫秒精度
            name, ext = os.path.splitext(original_name)
            return f"{timestamp}_{name}{ext}"
        
        elif self.naming_strategy == 'custom':
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S_%f')[:-3]
            name, ext = os.path.splitext(original_name)
            
            # 替换模板变量
            filename = self.custom_name_template.format(
                timestamp=timestamp,
                original_name=name,
                extension=ext.lstrip('.'),
                date=datetime.now().strftime('%Y%m%d'),
                time=datetime.now().strftime('%H%M%S')
            )
            return f"{filename}{ext}"
        
        else:
            self.logger.warning(f"未知的命名策略: {self.naming_strategy}，使用原始名称")
            return original_name
    
    def _handle_duplicate_file(self, filepath: str) -> str:
        """处理重复文件"""
        if not os.path.exists(filepath):
            return filepath
        
        if self.duplicate_handling == 'overwrite':
            return filepath
        
        elif self.duplicate_handling == 'skip':
            self.logger.info(f"文件已存在，跳过: {os.path.basename(filepath)}")
            return None
        
        elif self.duplicate_handling == 'rename':
            # 生成新的文件名
            base, ext = os.path.splitext(filepath)
            counter = 1
            
            while os.path.exists(f"{base}_{counter}{ext}"):
                counter += 1
            
            new_filepath = f"{base}_{counter}{ext}"
            self.logger.info(f"文件重命名: {os.path.basename(filepath)} -> {os.path.basename(new_filepath)}")
            return new_filepath
        
        return filepath

    def process_file(self, file_path: str):
        """处理文件：解析任务数据"""
        try:
            # 获取文件名
            filename = os.path.basename(file_path)
            self.logger.info(f"开始处理文件: {filename}")
            
            # 读取文件内容
            with open(file_path, 'rb') as f:
                content = f.read()
            
            # 解析任务信息
            task_info = self._parse_filename(filename)
            if not task_info:
                self.logger.debug(f"文件名格式不符合任务数据格式，跳过数据库存储: {filename}")
                return
            
            task_category = task_info['task_category']
            file_ext = task_info['file_ext']    
            
            # 如果文件名的任务类别为"0"，并且后缀为jpg，就不保存文件
            if self._is_origin_data(task_category, file_ext):
                self.logger.debug(f"跳过原始图像文件: {filename}")
                return
            
            # 如果启用了任务数据解析，尝试解析并存储任务数据
            if self.enable_task_data_parsing and self.db_manager:
                self._parse_and_store_task_data_by_category(task_info, content, file_path)

            return True
        
        except Exception as e:
            self.logger.error(f"处理文件失败 {file_path}: {e}")
            return False
    
    def _parse_filename(self, filename: str) -> Dict[str, str]:
        """
        解析文件名格式：任务id_任务类别_时间戳.ext 或 卫星id_0_时间戳.ext
        
        :param filename: 文件名
        :return: 解析后的任务信息字典
        """
        try:
            base_name = os.path.splitext(filename)[0]
            file_ext = os.path.splitext(filename)[1].lower()
            parts = base_name.split('_')
            
            if len(parts) < 3:
                return None
                
            sat_id = parts[0]
            task_category = parts[1]
            try:
                dt = parser.parse(parts[2])
                # 处理无时区的情况（视为UTC）
                if dt.tzinfo is None:
                    dt = dt.replace(tzinfo=datetime.timezone.utc)
                # 转换为Unix时间戳（秒）后转毫秒
                timestamp = int(dt.timestamp() * 1000)
            except Exception as e:
                timestamp = parts[2]
            
            return {
                'filename': filename,
                'sat_id': sat_id, 
                'task_category': task_category,
                'timestamp': timestamp,
                'file_ext': file_ext
            }
            
        except Exception as e:
            self.logger.error(f"解析文件名失败: {filename} - {e}")
            return None
    
    def _is_origin_data(self, task_category: str, file_ext: str) -> bool:
        """判断是否为源图像数据"""
        # 任务类别为"0"，文件后缀为.jpg
        return task_category == '0' and file_ext ==  '.jpg'
    
    def _is_feature_data(self, task_category: str) -> bool:
        """判断是否为特征数据"""
        # 算法类型
        algorithm_types = ['BHSF','WZZH','RHDW','XDSF']
        return task_category.upper() in algorithm_types
    
    def _is_decision_data(self, task_category: str) -> bool:
        """判断是否为决策数据"""
        # 决策类型
        decision_types = ['YTYP', 'TSFX']
        return task_category.upper() in decision_types
    
    def _parse_and_store_task_data_by_category(self, task_info: dict, content: bytes, original_abs_path: str):
        """
        根据任务类别解析文件并存储到对应的数据库表
        
        :param task_info: 任务信息
        :param content: 文件内容
        :param saved_filepath: 保存的文件路径
        :param original_abs_path: 原始绝对路径
        """
        try:
            filename = task_info['filename']
            sat_id = task_info['sat_id']
            task_category = task_info['task_category']
            timestamp = task_info['timestamp']
            file_ext = task_info['file_ext']
            
            self.logger.info(f"解析文件: {filename}, 卫星ID={sat_id}, 任务类别={task_category}, 时间戳={timestamp}")
            
            if task_category == '0' and file_ext == '.json':
                # 源数据 - 存储到 origional_table
                self._store_origin_data(filename, content, task_info)
                
            elif self._is_feature_data(task_category) and file_ext == '.json':
                # 特征数据 - 存储到 feature_table
                self._store_feature_data(filename, content, task_info)
                
            elif self._is_decision_data(task_category) and file_ext == '.json':
                # 决策数据 - 存储到 decision_table
                self._store_decision_data(filename, content, task_info)
                
            else:
                self.logger.warning(f"未知的任务类别或文件类型: {task_category} {file_ext}")
                
        except Exception as e:
            self.logger.error(f"解析并存储任务数据失败: {filename} - {e}")
    
    def _store_origin_data(self, filename: str, content: bytes, task_info: Dict[str, str]):
        """
        存储源数据到origional_table和truth_table表 - 优化版，使用新的插入函数
        
        :param filename: 文件名
        :param content: 文件内容
        :param saved_filepath: 保存的文件路径
        :param original_abs_path: 原始绝对路径
        :param task_info: 任务信息
        """
        try:
            # 解析JSON内容
            json_data = json.loads(content.decode('utf-8'))
            
            device_info = json_data.get('deviceInfo', {})
            selected_image = json_data.get('selectedImage', {})
            
            # 根据task.txt中的映射关系进行类型转换
            device_type_map = {0: 'GEO', 131072: 'LEO', 131073: 'LEO_SAR', 131074: 'LEO_RADAR', 131075: 'LEO_ELECTRO'}
            equip_type_map = {1: 'CCD', 2: 'IR', 3: 'SAR', 4: 'RADAR', 5: 'ELECTRO', 6: 'PAN'}
            target_type_map = {
                101: 'AIRPORT', 102: 'PORT', 1201: 'CARRIER', 1202: 'BATTLESHIP', 
                1203: 'DESTROYER', 1204: 'SUPPLY', 1205: 'FRIGATE'
            }
            
            # 提取基本信息用于origional_table
            origin_data = {
                'sat_id': str(task_info.get('sat_id', '')),
                'sat_category': device_type_map.get(device_info.get('deviceType', 0), 'OTHER'),
                'sat_load_category': equip_type_map.get(device_info.get('equipType', 0), 'OTHER'),
                'planed_start_time': str(device_info.get('planedStartTime', 0)),
                'planed_end_time': str(device_info.get('planedEndTime', 0)),
                'timestamp': str(task_info.get('timestamp', 0)),
                
                # 图像相关信息
                'image_size': selected_image.get('imageSize', 0.0),
                'image_name': selected_image.get('imageName', filename),
                'image_path': selected_image.get('imagePath', saved_filepath),
                'image_truth_width': selected_image.get('image_truth_width', 0.0),
                'image_resolution': selected_image.get('image_resolution', 0.0),
                
                # 视线中心点ENU坐标系信息
                'enu_base_longitude': selected_image.get('enuBaseLon', 0.0),
                'enu_base_latitude': selected_image.get('enuBaseLat', 0.0),
                'enu_base_altitude': selected_image.get('enuBaseAlt', 0.0),
                
                # 卫星位置信息
                'sat_enu_x': selected_image.get('satPosEnuX', 0.0),
                'sat_enu_y': selected_image.get('satPosEnuY', 0.0),
                'sat_enu_z': selected_image.get('satPosEnuZ', 0.0),
                
                # 相机四元数信息
                'cam_enu_x': selected_image.get('camQENU_X', 0.0),
                'cam_enu_y': selected_image.get('camQENU_Y', 0.0),
                'cam_enu_z': selected_image.get('camQENU_Z', 0.0),
                'cam_enu_w': selected_image.get('camQENU_W', 0.0),
                
                # J2000坐标系信息
                'sat_j2000_x': selected_image.get('satPosJ2000X', 0.0),
                'sat_j2000_y': selected_image.get('satPosJ2000Y', 0.0),
                'sat_j2000_z': selected_image.get('satPosJ2000Z', 0.0),
                
                # 四元数信息
                'sat_qbi_x': selected_image.get('satQbiX', 0.0),
                'sat_qbi_y': selected_image.get('satQbiY', 0.0),
                'sat_qbi_z': selected_image.get('satQbiZ', 0.0),
                'sat_qbi_w': selected_image.get('satQbiW', 0.0),
                
                # 载荷视场角
                'load_fov_x': selected_image.get('fovX', 0.0),
                'load_fov_y': selected_image.get('fovY', 0.0),
                
                # 图像像素信息
                'image_pixel_x': selected_image.get('camPixelX', 0.0),
                'image_pixel_y': selected_image.get('camPixelY', 0.0)
            }
            
            # 插入origional_table数据
            origin_record_id = self.db_manager.insert_origin_data(origin_data)
            if origin_record_id:
                self.logger.info(f"成功插入源数据，记录ID: {origin_record_id}")
            else:
                self.logger.error(f"插入源数据失败: {filename}")
                return
            
            # 处理目标信息，插入truth_table
            target_info_list = selected_image.get('targetInfo', [])
            target_num = selected_image.get('targetNum', 0)
            
            if target_num > 0 and target_info_list:
                for i, target_info in enumerate(target_info_list[:target_num]):
                    # 提取目标信息
                    drawbox = target_info.get('drawbox', {})
                    returnbox = target_info.get('returnbox', {})
                    
                    truth_data = {
                        'sat_id': str(task_info.get('sat_id', '')),
                        'timestamp': str(task_info.get('timestamp', 0)),
                        'target_id': str(target_info.get('targetID', '')),
                        'fleet_number': str(target_info.get('targetFleetNumber', '')),
                        'target_category': target_type_map.get(target_info.get('targetType', 0), 'OTHER'),
                        'target_direction': str(target_info.get('targetDirection', 0.0)),
                        
                        # 目标位置信息
                        'target_longitude': target_info.get('targetPosLon', 0.0),
                        'target_latitude': target_info.get('targetPosLat', 0.0),
                        'target_altitude': target_info.get('targetPosAlt', 0.0),
                        
                        # 卫星坐标系下的目标位置
                        'target_sat_x': target_info.get('targetPosInSatX', 0.0),
                        'target_sat_y': target_info.get('targetPosInSatY', 0.0),
                        'target_sat_z': target_info.get('targetPosInSatZ', 0.0),
                        
                        # drawbox信息
                        'target_draw_x': drawbox.get('x', 0.0),
                        'target_draw_y': drawbox.get('y', 0.0),
                        'target_draw_width': drawbox.get('width', 0.0),
                        'target_draw_height': drawbox.get('height', 0.0),
                        
                        # returnbox信息
                        'target_return_x': returnbox.get('x', 0.0),
                        'target_return_y': returnbox.get('y', 0.0),
                        'target_return_width': returnbox.get('width', 0.0),
                        'target_return_height': returnbox.get('height', 0.0)
                    }
                    
                    # 插入truth_table数据
                    truth_record_id = self.db_manager.insert_truth_data(truth_data)
                    if truth_record_id:
                        self.logger.debug(f"成功插入目标数据 {i+1}/{target_num}，记录ID: {truth_record_id}")
                    else:
                        self.logger.error(f"插入目标数据失败: {filename}, 目标 {i+1}")
                
                self.logger.info(f"处理了 {len(target_info_list[:target_num])} 个目标信息")
            else:
                self.logger.info(f"文件 {filename} 中没有目标信息")
                
            self.logger.info(f"源数据已成功存储到数据库: {filename}")
            
        except json.JSONDecodeError as e:
            self.logger.error(f"JSON解析失败 {filename}: {e}")
        except Exception as e:
            self.logger.error(f"存储源数据失败 {filename}: {e}")
            import traceback
            self.logger.error(f"详细错误信息: {traceback.format_exc()}")

    def _store_feature_data(self, filename: str, content: bytes,  task_info: Dict[str, str]):
        """
        存储特征数据到feature_table表
        
        :param filename: 文件名
        :param content: 文件内容
        :param task_info: 任务信息
        """
        try:
            # 如果是JSON文件，解析其中的特征数据
            if filename.lower().endswith('.json'):
                try:
                    json_data = json.loads(content.decode('utf-8'))
                    
                    # 处理不同的算法类型
                    task_category = task_info['task_category'].upper()
                    
                    if task_category == 'BHSF' or task_category == 'XDSF': 
                        self._process_identification_data(json_data, task_info)
                    elif task_category == 'WZZH':  # 位置转换
                        self._process_position_conversion_data(json_data, task_info)
                    elif task_category == 'RHDW':  # 融合定位
                        self._process_single_satellite_positioning_data(json_data, task_info)
                    else:
                        self.logger.warning(f"未知的算法类型: {task_category}")
                        
                except (json.JSONDecodeError, ValueError) as e:
                    self.logger.error(f"解析JSON特征数据失败: {filename} - {e}")
            else:
                self.logger.debug(f"非 JSON 文件，跳过特征数据解析: {filename}")
                
        except Exception as e:
            self.logger.error(f"存储特征数据失败: {filename} - {e}")
    
    def _store_decision_data(self, filename: str, content: bytes, task_info: Dict[str, str]):
        """
        存储决策数据到decision_table表
        
        :param filename: 文件名
        :param content: 文件内容
        :param saved_filepath: 保存的文件路径
        :param original_abs_path: 原始绝对路径
        :param task_info: 任务信息
        """
        try:
            # 如果是JSON文件，解析其中的决策数据
            if filename.lower().endswith('.json'):
                try:
                    json_data = json.loads(content.decode('utf-8'))
                    
                    # 准备基础决策数据
                    decision_data = {
                        'timestamp': task_info.get('timestamp', ''),
                        'fleet_number': json_data.get('fleet_number', ''),
                        'operate_begin': json_data.get('operate_begin', ''),
                        'operate_end': json_data.get('operate_end', '')
                    }
                    
                    # 根据不同的输出文件类型进行解析
                    if 'prediction_type' in json_data:  # 目标意图输出文件
                        # 意图研判结果
                        success = json_data.get('success', False)
                        decision_data['target_prediction'] = json_data.get('result', '')
                        decision_data['target_prediction_success'] = 1 if success else 0
                        
                    elif 'model_type' in json_data and json_data.get('model_type') == 'situation':  # 态势分析输出文件
                        # 态势分析结果
                        success = json_data.get('success', False)
                        result = json_data.get('result', {})
                        
                        # 解析态势分析结果
                        if isinstance(result, dict):
                            decision_data['speed_threat'] = result.get('speed_threat', 0.0)
                            decision_data['heading_threat'] = result.get('heading_threat', 0.0)
                            decision_data['distance_threat'] = result.get('distance_threat', 0.0)
                            decision_data['heading_angle'] = result.get('heading_angle', 0.0)
                            decision_data['target_analyse'] = f"速度威胁:{result.get('speed_threat', 0.0)}, 方向威胁:{result.get('heading_threat', 0.0)}, 距离威胁:{result.get('distance_threat', 0.0)}, 方向角度:{result.get('heading_angle', 0.0)}"
                        else:
                            decision_data['target_analyse'] = str(result)
                            
                        decision_data['target_analyse_success'] = 1 if success else 0
                    
                    # 插入数据库
                    record_id = self.db_manager.insert_decision_data(decision_data)
                    if record_id:
                        self.logger.info(f"决策数据存储成功: {filename} -> decision_table (ID: {record_id})")
                        self.logger.debug(f"决策数据详情: {decision_data}")
                    else:
                        self.logger.error(f"决策数据存储失败: {filename}")
                        
                except (json.JSONDecodeError, ValueError) as e:
                    self.logger.error(f"解析JSON决策数据失败: {filename} - {e}")
            else:
                self.logger.debug(f"非 JSON 文件，跳过决策数据解析: {filename}")
                
        except Exception as e:
            self.logger.error(f"存储决策数据失败: {filename} - {e}")
    
    def _process_identification_data(self, json_data: dict, task_info: Dict[str, str]):
        """处理14类算法数据 (BHSF)
        支持两种数据格式：
        1. 原有格式：直接包含targets数组或单个目标数据
        2. 新文件格式：包含image_path, num_targets, targets的结构
        """
        try:
            # 检测数据格式
            if isinstance(json_data, list):
                # 新文件格式：数组形式，每个元素包含image_path等信息
                self._process_identification(json_data, task_info)
            else:
                # 数据格式不正确
                self.logger.error(f"数据格式不正确，请检查{task_info['filename']}的文件格式")
        except Exception as e:
            self.logger.error(f"处理身份识别数据失败: {e}")
    
    def _process_identification(self, data_list: list, task_info: Dict[str, str]):
        for data_item in data_list:
            target_image_path = data_item.get('image_path','')
            target_id = data_item.get('target_id')
            target_category = data_item.get('type')
            target_truth = data_item.get('truth')
            position = data_item.get('position', {})
            target_x = position.get('x1', 0)
            target_y = position.get('y1', 0)
            target_width = position.get('width', 0)
            target_height = position.get('height', 0)
            target_npy_path = data_item.get('npy_path', '')
            self.logger.info(f"处理图像文件: {target_image_path}, 目标数量: {len(data_list)}")
            
            feature_data = {
                'sat_id': task_info['sat_id'],
                'fleet_number': 0,
                'timestamp': task_info['timestamp'],
                'target_category': target_category,
                'target_id': target_id,
                'target_image_path': target_image_path,
                'target_truth': target_truth,
                'target_x': target_x,
                'target_y': target_y,
                'target_width': target_width,
                'target_height': target_height,
                'target_npy_path': target_npy_path
            }
            
            record_id = self.db_manager.insert_feature_data(feature_data)
            if record_id:
                self.logger.info(f"身份识别数据存储成功: target_id={feature_data['target_id']}, "
                                f"type={feature_data['target_category']}, truth={feature_data['target_truth']} (ID: {record_id})")
    
    def _process_position_conversion_data(self, json_data: dict, task_info: Dict[str, str]):
        """处理位置转换(WZZH)算法数据
        使用sat_id, timestamp, target_id作为约束条件更新数据库
        """
        try:
            sat_id = task_info['sat_id']
            timestamp = task_info['timestamp']
            results = json_data.get('results', [])
            
            if not results:
                self.logger.warning(f"位置转换数据为空: sat_id={sat_id}")
                return
            
            self.logger.info(f"处理位置转换数据: sat_id={sat_id}, 目标数量={len(results)}")
            
            updated_count = 0
            not_found_count = 0
            
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                for result in results:
                    target_id = str(result.get('target_id', ''))
                    lat = float(result.get('Latitude', 0.0))  # 纬度
                    lon = float(result.get('longitude', 0.0))  # 经度
                    
                    # 检查约束条件
                    if not all([target_id, lat, lon]):
                        self.logger.warning(f"约束条件不完整: sat_id={sat_id}, timestamp={timestamp}, target_id={target_id}")
                        continue
                    
                    # 检查记录是否存在
                    cursor.execute("""
                        SELECT id FROM feature_table 
                        WHERE sat_id = ? AND timestamp = ? AND target_id = ?
                    """, (sat_id, timestamp, target_id))
                    
                    existing_record = cursor.fetchone()
                    
                    if existing_record:
                        # 更新现有记录
                        update_sql = """
                            UPDATE feature_table 
                            SET target_longitude = ?, target_latitude = ?
                            WHERE sat_id = ? AND timestamp = ? AND target_id = ?
                        """
                        
                        cursor.execute(update_sql, (lon, lat, sat_id, timestamp, target_id))
                        updated_count += 1
                        
                        self.logger.info(f"更新位置信息成功: target_id={target_id}, "
                                       f"lat={lat}, lon={lon} (ID: {existing_record[0]})")
                    else:
                        # 插入新记录
                        feature_data = {
                            'sat_id': sat_id,
                            'target_id': target_id,
                            'timestamp': timestamp,
                            'target_longitude': lon,
                            'target_latitude': lat
                        }
                        
                        # 使用现有的insert_feature_data方法插入
                        record_id = self.db_manager.insert_feature_data(feature_data)
                        if record_id:
                            not_found_count += 1  # 计为新插入记录
                            self.logger.info(f"插入新的位置记录: target_id={target_id}, "
                                           f"lat={lat}, lon={lon}, (ID: {record_id})")
                        else:
                            self.logger.error(f"插入新记录失败: target_id={target_id}")
                
                conn.commit()
                
            self.logger.info(f"位置转换数据处理完成: "
                           f"更新{updated_count}条记录, 未找到{not_found_count}条记录")
                    
        except Exception as e:
            self.logger.error(f"处理位置转换数据失败: {e}")
    
    def _process_single_satellite_positioning_data(self, json_data: dict, task_info: Dict[str, str]):
        """处理融合定位(RHDW)算法数据
        使用sat_id, timestamp, target_id作为约束条件更新/插入数据库
        """
        try:
            sat_id = task_info['sat_id']
            timestamp = task_info['timestamp']
            fused_positions = json_data.get('fused_positions', [])
            
            if not fused_positions:
                self.logger.warning(f"融合定位数据为空: sat_id={sat_id}")
                return
            
            self.logger.info(f"处理融合定位数据: sat_id={sat_id}, 目标数量={len(fused_positions)}")
            
            updated_count = 0
            inserted_count = 0
            
            # 目标类型映射
            target_type_map = {
                101: 'AIRPORT', 102: 'PORT', 1201: 'CARRIER', 1202: 'BATTLESHIP', 
                1203: 'DESTROYER', 1204: 'SUPPLY', 1205: 'FRIGATE'
            }
            
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                for position in fused_positions:
                    target_id = str(position.get('target_id', ''))
                    target_type = position.get('target_type', 0)
                    fused_lon = float(position.get('fused_lon', 0.0))
                    fused_lat = float(position.get('fused_lat', 0.0))
                    lon_velocity = float(position.get('lon_velocity', 0.0))
                    lat_velocity = float(position.get('lat_velocity', 0.0))
                    speed = float(position.get('speed', 0.0))
                    
                    # 检查约束条件
                    if not all([sat_id, timestamp, target_id]):
                        self.logger.warning(f"约束条件不完整: sat_id={sat_id}, timestamp={timestamp}, target_id={target_id}")
                        continue
                    
                    # 检查记录是否存在
                    cursor.execute("""
                        SELECT id FROM feature_table 
                        WHERE sat_id = ? AND timestamp = ? AND target_id = ?
                    """, (sat_id, timestamp, target_id))
                    
                    existing_record = cursor.fetchone()
                    
                    if existing_record:
                        # 更新现有记录
                        update_sql = """
                            UPDATE feature_table 
                            SET target_fusion_longitude = ?, target_fusion_latitude = ?,
                                target_longitude_speed = ?, target_latitude_speed = ?,
                                target_total_speed = ?, target_category = ?
                            WHERE sat_id = ? AND timestamp = ? AND target_id = ?
                        """
                        
                        target_category = target_type_map.get(target_type, 'OTHER')
                        cursor.execute(update_sql, (fused_lon, fused_lat, lon_velocity, lat_velocity, 
                                                   speed, target_category, sat_id, timestamp, target_id))
                        updated_count += 1
                        
                        self.logger.info(f"更新融合位置信息成功: target_id={target_id}, "
                                       f"fused_lon={fused_lon}, fused_lat={fused_lat}, speed={speed} (ID: {existing_record[0]})")
                    else:
                        # 插入新记录
                        feature_data = {
                            'sat_id': sat_id,
                            'target_id': target_id,
                            'target_category': target_type_map.get(target_type, 'OTHER'),
                            'timestamp': timestamp,
                            'target_fusion_longitude': fused_lon,
                            'target_fusion_latitude': fused_lat,
                            'target_longitude_speed': lon_velocity,
                            'target_latitude_speed': lat_velocity,
                            'target_total_speed': speed,
                            'target_truth': True
                        }
                        
                        # 使用现有的insert_feature_data方法插入
                        record_id = self.db_manager.insert_feature_data(feature_data)
                        if record_id:
                            inserted_count += 1
                            self.logger.info(f"插入新的融合位置记录: target_id={target_id}, "
                                           f"type={feature_data['target_category']}, fused_lon={fused_lon}, fused_lat={fused_lat}, speed={speed} (ID: {record_id})")
                        else:
                            self.logger.error(f"插入新记录失败: target_id={target_id}")
                
                conn.commit()
                
            self.logger.info(f"融合定位数据处理完成: "
                           f"更新{updated_count}条记录, 新插入{inserted_count}条记录")
                    
        except Exception as e:
            self.logger.error(f"处理融合定位数据失败: {e}")

    def start(self):
        """启动文件监控"""
        try:
            # 确保所有监控目录都存在
            for watch_dir in self.watch_dirs:
                os.makedirs(watch_dir, exist_ok=True)
                self.logger.info(f"确保目录存在: {watch_dir}")
            
            # 为每个监控目录创建观察者
            for watch_dir in self.watch_dirs:
                observer = Observer()
                observer.schedule(self.event_handler, watch_dir, recursive=True)
                observer.start()
                self.observers.append(observer)
                self.logger.info(f"开始监控目录: {watch_dir}")
            
            self.running = True
            
            # 保持主线程运行
            while self.running:
                time.sleep(1)
                
        except KeyboardInterrupt:
            self.stop()
        except Exception as e:
            self.logger.error(f"启动过程中发生错误: {e}")
            self.stop()

    def stop(self):
        """停止文件监控"""
        if self.running:
            self.running = False
            
            # 停止所有观察者
            for observer in self.observers:
                observer.stop()
                observer.join()
                self.logger.info(f"已停止监控目录: {observer.name}")
            
            self.observers = []
            self.logger.info("文件监控已完全停止")

    def get_database_manager(self) -> Optional[DatabaseManager]:
        """
        获取数据库管理器实例

        :return: 数据库管理器实例，如果未启用则返回None
        """
        return self.db_manager


if __name__ == "__main__":
    try:
        # 创建Frame接收器实例
        receiver = FrameReceiver()

        # 启动接收器
        receiver.start()

    except FileNotFoundError as e:
        print(f"错误: {e}")
        print("请确保 config.ini 配置文件存在于当前目录中")
    except Exception as e:
        print(f"启动失败: {e}")