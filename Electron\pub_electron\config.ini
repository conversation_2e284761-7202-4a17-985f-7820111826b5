# Leo 低轨卫星文件监视系统配置文件
# 修改此文件后需要重启服务才能生效

[satellite]
# 监视目录路径（多个目录用分号分隔）
watch_dirs = /home/<USER>/home/<USER>
# 是否递归监视子目录
recursive = true
# 实例标识（用于在消息中标识来源，可以是任意字符串）
instance_name = default
# 源图像文件后缀（用于判断是否跳过）
source_image_extensions = .jpg,.jpeg,.png
# 任务类别标识（文件名中第二部分为此值时视为源图像）
source_task_category = 0

[zmq]
# ZeroMQ连接地址（{高轨IP}:13236）
connect_address = tcp://0.0.0.0:13238
# 发布主题（所有Leo使用相同主题）
topic = file_data
# ZeroMQ连接延迟时间（秒）
linger_time = 0.5

[file_processing]
# 文件完整性检查最大尝试次数
max_attempts = 5
# 文件大小检查间隔（秒）
check_interval = 0.2
# 文件传输完成后是否保留源文件
keep_source_files = true

[logging]
# 日志级别：DEBUG, INFO, WARNING, ERROR, CRITICAL
level = INFO
# 日志格式（注意：INI文件中%需要转义为%%）
format = [%%(asctime)s] [%%(name)s] %%(message)s
# 日期格式（注意：INI文件中%需要转义为%%）
date_format = %%Y-%%m-%%d %%H:%%M:%%S

[subscriber]
# 测试订阅者连接地址
address = tcp://localhost:5556
# 接收文件保存目录
received_dir = received
