中间结果命名格式（卫星id_任务类别_时间戳.json）
源文件命名格式（卫星id_0_时间戳.jpg/json）

1、查明输入输出的每个字段的含义

 算法1~14：输入信息：源图像路径，输出信息：{卫星id}_BHSF_{时间戳}.json文件
 文件定义如下：
[
    {
        "image_path": "/home/<USER>/modelv5/overlap/yolov8/101_10001_patch_0000.jpg",
        "target_id": 1,
        "type": 1,
        "truth": "real",
        "position": {
            "x1": 197.20660228939744,
            "y1": 206.09083512738442,
            "width": 102.41095521296437,
            "height": 95.41525934394956
        }
    },
    ...(具有多段数据)
]

算法位置转换：输入信息：{卫星id}_{BHSF}_{时间戳}.json文件，输出信息：{卫星id}_{WZZH}_{时间戳}.json文件
输入文件定义如下：
{
  "targets":[
    {
      "sat_id": 10,
      "target_id": "SHIP_008",
      "timestamp": "1754665978890",
      "a": 116.62950000000001,
      "b": 40.1137,
      "cx": 116.4295,
      "cy": 40.0137,
      "fx": 116.4295,
      "fy": 40.0137,
      "pixel_x": 290,
      "pixel_y": 232,
      "pitch": 23.313143,
      "roll": 0.1234324234,
      "yaw": 208.80000000000007,
      "position": [300,200,100],
      "time": 11200
    },
    ...
  ]
}
输出文件定义如下：
{
  "sat_id": "11",
  "timestamp": "11111111",
  "results": [
    {
      "target_id": "SHIP_008",
      "Latitude": -85.20133562694265,
      "longitude": -46.70833333333323
    },
    ...(具有多段数据)
  ]
}

算法单星定位：输入信息：{卫星id}_{WZZH}_{时间戳}.json文件，输出信息：{卫星id}_{DXDW}_{时间戳}.json文件
输入文件定义如下：
{
    "parameters": {
        "process_noise": 0.02,
        "measurement_noise": 0.5,
        "association_threshold": 0.02
    },
    "observations": [
        {
            "target_id":1,
            "target_type": 1,
            "lon": 116.3,
            "lat": 39.9,
            "timestamp": 1111111112
        },
        {
            "target_id":2,
            "target_type": 1,
            "lon": 116.302,
            "lat": 39.901,
            "timestamp": 1111111112
        },
        {
            "target_id":3,
            "target_type": 2,
            "lon": 116.3978,
            "lat": 39.909,
            "timestamp": 1111111112
        },
        {
            "target_id":3,
            "target_type": 2,
            "lon": 116.4,
            "lat": 39.95,
            "timestamp": 1111111122
        },
        {
            "target_id":2,
            "target_type": 1,
            "lon": 116.3977,
            "lat": 39.9089,
            "timestamp": 1111111122
        },
        {
            "target_id":1,
            "target_type": 1,
            "lon": 116.3877,
            "lat": 39.9082,
            "timestamp": 1111111122
        }
    ]
}
输出文件定义如下：
{
  "fused_positions": [
    {
      "target_id": 1,
      "target_type": 1,
      "fused_lon": 116.343726,
      "fused_lat": 39.904088,
      "lon_velocity": 0.008795,
      "lat_velocity": 0.000822,
      "speed": 0.008833,
      "last_update": 1111111122
    },
    {
      "target_id": 2,
      "target_type": 1,
      "fused_lon": 116.349715,
      "fused_lat": 39.904939,
      "lon_velocity": 0.009597,
      "lat_velocity": 0.000792,
      "speed": 0.00963,
      "last_update": 1111111122
    },
    {
      "target_id": 3,
      "target_type": 2,
      "fused_lon": 116.398897,
      "fused_lat": 39.929442,
      "lon_velocity": 0.000221,
      "lat_velocity": 0.004112,
      "speed": 0.004117,
      "last_update": 1111111122
    }
  ]
}

算法态势分析：输入信息：{卫星id}_{TSFX}_{时间戳}.json文件，输出信息：{卫星id}_{TSFX}_{时间戳}.json文件
输入文件定义如下：（时间戳从低到高）
{
  "query_days": "3",
  "operate_begin": "1753465205901",
  "operate_end": "1753860177329",
  "FLEET_01": {
    "time_series_data": [
    {
      "SHIP_008": {
        "ship_type": 2,
        "latitude": 39.954499999999996,
        "longitude": 116.35749999999999,
        "timestamp": 1753465205901
      },
      "SHIP_007": {
        "ship_type": 2,
        "latitude": 39.944199999999995,
        "longitude": 116.34700000000001,
        "timestamp": 1753465205901
      },
      "SHIP_006": {
        "ship_type": 2,
        "latitude": 39.9339,
        "longitude": 116.3365,
        "timestamp": 1753465205901
      },
      "SHIP_005": {
        "ship_type": 2,
        "latitude": 39.9236,
        "longitude": 116.326,
        "timestamp": 1753465205901
      },
      "SHIP_004": {
        "ship_type": 2,
        "latitude": 39.9133,
        "longitude": 116.3155,
        "timestamp": 1753465205901
      },
      "SHIP_003": {
        "ship_type": 2,
        "latitude": 39.903,
        "longitude": 116.30499999999999,
        "timestamp": 1753465205901
      },
      "SHIP_002": {
        "ship_type": 2,
        "latitude": 39.9927,
        "longitude": 116.3945,
        "timestamp": 1753465205901
      },
      "SHIP_001": {
        "ship_type": 2,
        "latitude": 39.912,
        "longitude": 116.32,
        "timestamp": 1753465205901
      }
    },
    ...
  ]
  },
  "FLEET_02": {
    "time_series_data": [
    {
      "SHIP_007": {
        "ship_type": 2,
        "latitude": 39.944199999999995,
        "longitude": 116.34700000000001,
        "timestamp": 1753465205901
      },
      "SHIP_006": {
        "ship_type": 2,
        "latitude": 39.9339,
        "longitude": 116.3365,
        "timestamp": 1753465205901
      },
      "SHIP_005": {
        "ship_type": 2,
        "latitude": 39.9236,
        "longitude": 116.326,
        "timestamp": 1753465205901
      },
      "SHIP_004": {
        "ship_type": 2,
        "latitude": 39.9133,
        "longitude": 116.3155,
        "timestamp": 1753465205901
      },
      "SHIP_003": {
        "ship_type": 2,
        "latitude": 39.903,
        "longitude": 116.30499999999999,
        "timestamp": 1753465205901
      },
      "SHIP_002": {
        "ship_type": 2,
        "latitude": 39.9927,
        "longitude": 116.3945,
        "timestamp": 1753465205901
      },
      "SHIP_001": {
        "ship_type": 2,
        "latitude": 39.912,
        "longitude": 116.32,
        "timestamp": 1753465205901
      }
    },
    ...
    ]
  },
  "FLEET_03": {
    "time_series_data": [
    {
      "SHIP_006": {
        "ship_type": 2,
        "latitude": 39.9339,
        "longitude": 116.3365,
        "timestamp": 1753465205901
      },
      "SHIP_005": {
        "ship_type": 2,
        "latitude": 39.9236,
        "longitude": 116.326,
        "timestamp": 1753465205901
      },
      "SHIP_004": {
        "ship_type": 2,
        "latitude": 39.9133,
        "longitude": 116.3155,
        "timestamp": 1753465205901
      },
      "SHIP_003": {
        "ship_type": 2,
        "latitude": 39.903,
        "longitude": 116.30499999999999,
        "timestamp": 1753465205901
      },
      "SHIP_002": {
        "ship_type": 2,
        "latitude": 39.9927,
        "longitude": 116.3945,
        "timestamp": 1753465205901
      },
      "SHIP_001": {
        "ship_type": 2,
        "latitude": 39.912,
        "longitude": 116.32,
        "timestamp": 1753465205901
      }
    },
    ...
    ]
  }
}
输出文件定义如下：  
{
  "success": true,
  "query_days": "3",
  "operate_begin": "1753465205901",
  "operate_end": "1753860177329",
  "FLEET_01":{
    "intention": "xunluo",
    "situation": {
      "speed_threat": 0.0001,
      "heading_threat": 0.0175,
      "distance_threat": 0.0068,
      "heading_angle": 0.29
    }
  },
  "FLEET_02":{
    "intention": "xunluo",
    "situation": {
      "speed_threat": 0.0001,
      "heading_threat": 0.0175,
      "distance_threat": 0.0068,
      "heading_angle": 0.29
    }
  },
  "FLEET_03":{
    "intention": "xunluo",
    "situation": {
      "speed_threat": 0.0001,
      "heading_threat": 0.0175,
      "distance_threat": 0.0068,
      "heading_angle": 0.29
    }
  }
}

电侦智能算法：
输入：npy文件路径
输出：
[
    {
        "image_path": "/home/<USER>/Desktop/data_code/data_result/154_0_1820974011000_radar3.0.png",
        "npy_path": "/home/<USER>/Desktop/data_code/data_result/154_0_1820974011000_radar3.0.npy",
        "type": 1203,
        "target_id": 1012
    },
    {
        "image_path": "/home/<USER>/Desktop/data_code/data_result/154_0_1820974011000_radar3.0.png",
        "npy_path": "/home/<USER>/Desktop/data_code/data_result/154_0_1820974011000_radar3.0.npy",
        "type": 1203,
        "target_id": 1013
    },
    {
        "image_path": "/home/<USER>/Desktop/data_code/data_result/154_0_1820974011000_radar3.0.png",
        "npy_path": "/home/<USER>/Desktop/data_code/data_result/154_0_1820974011000_radar3.0.npy",
        "type": 1203,
        "target_id": 1014
    },
    {
        "image_path": "/home/<USER>/Desktop/data_code/data_result/154_0_1820974011000_radar2.0.png",
        "npy_path": "/home/<USER>/Desktop/data_code/data_result/154_0_1820974011000_radar2.0.npy",
        "type": 1202,
        "target_id": 1010
    },
    {
        "image_path": "/home/<USER>/Desktop/data_code/data_result/154_0_1820974011000_radar2.0.png",
        "npy_path": "/home/<USER>/Desktop/data_code/data_result/154_0_1820974011000_radar2.0.npy",
        "type": 1202,
        "target_id": 1011
    },
    {
        "image_path": "/home/<USER>/Desktop/data_code/data_result/154_0_1820974011000_radar4.0.png",
        "npy_path": "/home/<USER>/Desktop/data_code/data_result/154_0_1820974011000_radar4.0.npy",
        "type": 1201,
        "target_id": 1009
    },
    {
        "image_path": "/home/<USER>/Desktop/data_code/data_result/154_0_1820974011000_radar1.0.png",
        "npy_path": "/home/<USER>/Desktop/data_code/data_result/154_0_1820974011000_radar1.0.npy",
        "type": 1204,
        "target_id": 1015
    }
]

雷达智能算法：
输入：json文件路径
输出：待补充

2、想尽办法找到所有的输入数据，（电帧、sar、可见光、红外、全色、雷达），以图像为单位-》源数据（origional_table）
 
 源数据表（origional_table）
 ========================
 卫星id（sat_id）、
 卫星类别（sat_category）、
 卫星所带载荷类别（sat_load_category）、
 任务开始时间（planed_start_time）、
 任务结束时间（planed_end_time）、
 图像的大小（image_size）、
 图像名称（image_name）、
 图像保存的路径（image_path）、
 图像的幅宽（image_truth_width）、
 图像的分辨率（image_resolution）
 时间戳（timestamp）、

 卫星的经度（enu_base_longitude）、
 卫星的维度（enu_base_latitude）、
 卫星的高度（enu_base_altitude）、
 卫星的ENU位置X（sat_enu_x）、
 卫星的ENU位置Y（sat_enu_y）、
 卫星的ENU位置Z（sat_enu_z）、
 卫星载荷的ENU四元数X（cam_enu_x）、
 卫星载荷的ENU四元数Y（cam_enu_y）、
 卫星载荷的ENU四元数Z（cam_enu_z）、
 卫星载荷的ENU四元数W（cam_enu_w）、
 卫星的J2000X（sat_j2000_x）、
 卫星的J2000Y（sat_j2000_y）、
 卫星的J2000Z（sat_j2000_z）、
 卫星的姿态四元数x（sat_qbi_x）、
 卫星的姿态四元数y（sat_qbi_y）、
 卫星的姿态四元数z（sat_qbi_z）、
 卫星的姿态四元数w（sat_qbi_w）、
 载荷的视场角x（load_fov_x）、
 载荷的视场角y（load_fov_y）、
 像素宽度（image_pixel_x）、
 像素高度（image_pixel_y）、
 

 真值表（truth_table）
 ========================
 卫星id（sat_id）、
 时间戳（timestamp）、
 目标id（target_id）、
 目标编队（fleet_number）、
 目标类别（target_category）、
 目标方向（target_direction）、
 目标经度（target_longitude）、
 目标维度（target_latitude）、
 目标高度（target_altitude）、
 目标在卫星本体坐标系下的z值（target_sat_z）、
 目标在卫星本体坐标系下的y值（target_sat_y）、
 目标在卫星本体坐标系下的x值（target_sat_x）、
 目标所在图像中的X像素坐标（target_draw_x）、
 目标所在图像中的Y像素坐标（target_draw_y）、
 目标所在图像的宽（target_draw_width）、
 目标所在图像的高（target_draw_height）、
 目标真实的X像素坐标（target_return_x）、
 目标真实的Y像素坐标（target_return_y）、
 目标真实的宽（target_return_width）、
 目标真实的高（target_return_height）、

3、所有算法的输出结果，以目标为单位-》特征数据（feature_table）
特征数据表（feature_table）
========================
 卫星id（sat_id）、
 舰队编号（fleet_number）、
 目标类别（target_category）、
 目标id（target_id）、
 时间戳（timestamp）、
 目标图像所在的文件路径（target_image_path）、
 目标真伪（target_truth）、
 目标在图像中的x（target_x）、
 目标在图像中的y（target_y）、
 目标框在图像中的宽度（target_width）
 目标框在图像中的高度（target_height）
 目标的经度（target_longitude）、
 目标的维度（target_latitude）、
 目标的融合经度（target_fusion_longitude）、
 目标的融合维度（target_fusion_latitude）、
 目标的经度速度（target_longitude_speed）、
 目标的维度速度（target_latitude_speed）、
 目标的总速度（target_total_speed）、
 目标npy所在的文件路径（target_npy_path）、

4、意图研判、态势分析的结果数据，以编队为单位-》决策数据（decision_table）
决策数据表（decision_table）
========================
 卫星id（sat_id）、
 时间戳（timestamp）、
 舰队编号（fleet_number）、
 速度威胁（speed_threat）、
 航向威胁（heading_threat）、
 距离威胁（distance_threat）、
 航向角（heading_angle）、
 开始时间（operate_begin）、
 截止时间（operate_end）、
 舰队意图（fleet_prediction）、
 
北控将源数据下发给北邮，北邮将图像与对应配置文件，传输到指定低轨，指定低轨再将配置文件上传到电帧，电帧解析文件，将数据更新到高轨数据库；电帧将文件同步至高轨,高轨解析文件，将数据更新到数据库

访问数据实现：通过fastapi，在高轨开放查询接口，平台通过查询高轨接口，返回算法的输入文件路径，并且将文件同步至低轨卫星

低轨、
1、PUB,leo，cluster，{*}:13215,(监听并发布)/home/<USER>/：监听北邮将源文件上传到指定星结点的源文件（任务源图像和附属源文件）、算法中间结果文件，上传到电帧卫星

步骤： 1-监听到有新文件传输，获取文件后缀，获取文件名（卫星id_0_时间戳.jpg/json）；
      2-根据文件后缀（jpg）+文件名，判断是否为源图像
      3-如果是源图像数据，跳过
      4-如果不是源图像数据（json），发布完整文件内容（文件名，文件绝对路径，文件内容）


6、SUB,leo, fileSync, {localhost}:13217,(保存在)./tmp/Data：接收高轨卫星下发的结果文件，供算法使用
步骤： 1-接收高轨卫星下发的结果文件

电帧、
2、SUB,frame, cluster, {localhost}13215,/frame_data：接收低轨上传的源文件，并解析文件内容，更新数据库(切分图像路径：/home/<USER>/dataShare/leo/data/slice/{任务id})

步骤： 1-接收低轨上传的源文件
      2-解析文件内容（按照任务类别进行解析），更新数据库
            “0”-附属源文件-更新数据库源数据表（origion_table）
            “SFSB\SSSB\ZWFL\DMBSB\WZZH\DXDW”-中间算法文件-更新数据库特征数据表（feature_table）
            “YTYP\TSFX”-决策结果文件-更新数据库决策数据表（decision_table）
            
附属源文件格式定义：
{
  "deviceInfo": {
    "deviceID": 1001,(对应数据库中sat_id)                     // 1~308，每个卫星对应的ID(任务下发)
    "deviceType": 0,(对应数据库中sat_category)                // 0高轨光学 131072低轨光学（可见光红外全色）131073低轨SAR 131074低轨雷达 131075低轨电侦(任务下发)
    "equipType": 3,(对应数据库中sat_load_category)            // 载荷类型 1可见光 2红外 3SAR 4雷达 5电侦 6全色(任务下发)
    "planedStartTime": 0,(对应数据库中planed_start_time)      // 任务开始时间，2023.1.1 00:00:00 UTC 经过的累计秒数(任务下发)
    "planedEndTime": 0,(对应数据库中planed_end_time)          // 任务结束时间，2023.1.1 00:00:00 UTC 经过的累计秒数(任务下发)
  },
  "selectedImage": {
    "imageSize": 276,(单位：MB)（对应数据库中image_size）      // 这张图像的大小(生成图像后，在引擎读取)
    "imageName": "image_name",(对应数据库中image_name)        // 这张图象的名称(格式：{deviceID}_{0}_{unixTime}.jpg，公式：long unixTime = long(planedEndTime) * 1000 + 1672502400000)
    "imagePath": "image_path",(对应数据库中image_path)        // 这张图象的绝对路径(默认值：/home/<USER>/dataShare/origional/{deviceID}_{0}_{unixTime}.jpg)
    "image_truth_width": 1920,(对应数据库中image_truth_width) // 这张图象的幅宽(根据载荷类型不同，设为定值)(可见光：100km*100km，红外：100km*100km，全色：12km*12km，SAR：12km*12km，雷达：12km*12km)
    "image_resolution": 5,(对应数据库中image_resolution) // 这张图象的分辨率(根据载荷类型不同，设为定值)(可见光：5m，红外：50m，全色：0.5m，SAR：5m，雷达：5m，电侦：5m)
    "obsTime": 1672538400,(单位：毫秒)（对应数据库中timestamp）// 2023.1.1 00:00:00 UTC 经过的累计秒数(任务下发)
    "enuBaseLon": 121.4737,(对应数据库中enu_base_longitude)         // 卫星视线中心点的经度(任务下发)
    "enuBaseLat": 31.2304,(对应数据库中enu_base_latitude)         // 卫星视线中心点的纬度(任务下发)
    "enuBaseAlt": 510.0,(对应数据库中enu_base_altitude)            // 卫星视线中心点的高度(任务下发)
    "satPosEnuX": 121.4737,(对应数据库中sat_enu_x)       // 卫星东北天坐标系的x(任务下发)
    "satPosEnuY": 31.2304,(对应数据库中sat_enu_y)        // 卫星东北天坐标系的y(任务下发)
    "satPosEnuZ": 510.0,(对应数据库中sat_enu_z)          // 卫星东北天坐标系的z(任务下发)
    "camQENU_X": 0.0,(对应数据库中cam_enu_x)    // 相机东北天坐标系的X(任务下发)
    "camQENU_Y": 0.0,(对应数据库中cam_enu_y)    // 相机东北天坐标系的Y(任务下发)
    "camQENU_Z": 0.0,(对应数据库中cam_enu_z)    // 相机东北天坐标系的Z(任务下发)
    "camQENU_W": 0.0,(对应数据库中cam_enu_w)    // 相机东北天坐标系的W(任务下发)
    "satPosJ2000X": 0.0,(对应数据库中sat_j2000_x)    // 卫星J2000坐标系的X(任务下发)
    "satPosJ2000Y": 0.0,(对应数据库中sat_j2000_y)   // 卫星J2000坐标系的Y(任务下发)
    "satPosJ2000Z": 0.0,(对应数据库中sat_j2000_z)    // 卫卫星J2000坐标系的Z(任务下发)
    "satQbiX": 0.0,(对应数据库中sat_qbi_x)                    // 卫星J2000坐标系的x(任务下发)
    "satQbiY": 0.0,(对应数据库中sat_qbi_y)                    // 卫星J2000坐标系的y(任务下发)
    "satQbiZ": 0.0,(对应数据库中sat_qbi_z)                    // 卫星J2000坐标系的z(任务下发)
    "satQbiW": 0.0,(对应数据库中sat_qbi_w)                    // 卫星J2000坐标系的w(任务下发)
    "fovX": 30.0,(对应数据库中load_fov_x)                     // 载荷视场角x(任务下发)
    "fovY": 0.0,(对应数据库中load_fov_y)                      // 载荷视场角y(任务下发)
    "camPixelX": 1920.0,(对应数据库中image_pixel_x)           // 载荷x分辨率(生成图像时，设定的x像素分辨率)
    "camPixelY": 0.0,(对应数据库中image_pixel_y)              // 载荷y分辨率(生成图像时，设定的y像素分辨率)
    "targetNum": 1,                                          // 图像中的目标数量(通过target的长度获得)
    "targetInfo": [
      {
        "targetID": 0,(对应数据库中target_id)                 // (0,1000) = base [1000,2000) = ship(任务下发)
        "targetFleetNumber": "FLEET_01",(对应数据库中fleet_number)(当targetID不是ship时，默认为0) // 当
        "targetType": 0,(对应数据库中target_category)         // 101机场 102港口 1201航母 1202巡洋舰 1203驱逐舰 1204补给舰 1205护卫舰(任务下发)
        "targetDirection": 0.0,(对应数据库中target_direction) // 目标的方向 east = 0 north = PI/2 rad(任务下发)
        "targetPosLon": 0.0,(对应数据库中target_longitude)    // 目标的经度(任务下发)
        "targetPosLat": 0.0,(对应数据库中target_latitude)     // 目标的维度(任务下发)
        "targetPosAlt": 0.0,(对应数据库中target_altitude)     // 目标的高度(任务下发)
        "targetPosInSatZ": 0.0,(对应数据库中target_sat_z)   // 目标在卫星本体坐标系下的z值(任务下发)
        "targetPosInSatY": 0.0,(对应数据库中target_sat_y)    // 目标在卫星本体坐标系下的y值(任务下发)
        "targetPosInSatX": 0.0,(对应数据库中target_sat_x)    // 目标在卫星本体坐标系下的x值(任务下发)
        "drawbox":{         // 要在图像上标注的标框信息(引擎基于目标点计算)
          "x": 100,         // 中心点的x像素坐标
          "y": 100,         // 中心点的y像素坐标
          "width": 100,     // 标框的宽度
          "height": 100     // 标框的高度
        },
        "returnbox":{       // 要返回结果的真实数值(任务下发)
          "x": 100,         // 中心点的x像素坐标
          "y": 100,         // 中心点的y像素坐标
          "width": 100,     // 标框的宽度
          "height": 100     // 标框的高度
        }
      },
      ...
    ]
  }
}
3、PUB,Leo,clusterGeo,{*}:13216,/frame_data：将低轨上传的文件数据，上传到高轨卫星
步骤： 1-将低轨上传的文件数据，上传到高轨卫星

6、SUB,leo, fileSync, {localhost}:13217,./tmp/Data：接收高轨卫星下发的结果文件，供算法使用
步骤： 1-接收高轨卫星下发的结果文件

高轨、
4、SUB,frame,clusterGeo, {localhost}:13216,/home/<USER>/GeoData：接收电帧上传的文件，并解析文件内容，更新数据库数据表
步骤： 1-接收电帧上传的源文件
      2-解析文件内容（按照任务类别进行解析），更新数据库
            “0”-附属源文件-更新数据库源数据表（origion_table）
            “SFSB\SSSB\ZWFL\DMBSB\WZZH\DXDW”-中间算法文件-更新数据库特征数据表（feature_table）
            “YTYP\TSFX”-决策结果文件-更新数据库决策数据表（decision_table）

5、PUB,Fastapi,fileSync, {*}:13217,/tmp/Data：高轨开放数据库操作API，让各卫星节点对数据库进行增删改查，并且将结果文件发布出去，让各个卫星接收同步
步骤： 1-高轨开放各个算法输入数据的查询接口，让各卫星节点数据库进行查询
      2-将结果文件发布出去，让各个卫星接收同步

7、PUB,leo,clusterGeo,{*}:13216,/home/<USER>/：监听高轨的任务结果数据，发给高轨解析，存入数据库
步骤： 1-监听高轨的任务结果数据
      2-发给高轨解析，存入数据库

数据的查询接口：
1、进行位置转换（get）：device_id、timestamp
{GeoIP}:13231/tasks/position/?device_id=123&timestamp=123456

2、进行融合定位（get）：device_id、timestamp
{GeoIP}:13231/tasks/fusion/?device_id=123&timestamp=123456

3、进行态势研判（get）：style(可选3/7，默认为"3")
{GeoIP}:13231/tasks/analyse/?style=3


高轨负责，
  6、监听指定目录文件/home/<USER>/home/<USER>

  7、对外开放查询接口，并将结果保存到/home/<USER>/api_result/；0.0.0.0:13231

  8、监听/home/<USER>/api_result/将文件PUB到所有geo_data-0.0.0.0:13239；

  5、SUB电侦发送的文件，保存到文件的绝对路径下file_data-localhost:13238

电侦负责，
  3、监听指定目录文件/home/<USER>/home/<USER>

  0、对外开放查询接口，并将结果保存到/home/<USER>/api_result/；0.0.0.0:13231

  0、监听/home/<USER>/api_result/将文件PUB到所有leo_data-0.0.0.0:13237；

  4、监听/home/<USER>/home/<USER>

  9、SUB高轨发送的文件，保存到文件的绝对路径下geo_data-localhost:13239

  2、SUB低轨发送的文件，保存到文件的绝对路径下leo_data-localhost:13236

低轨负责，
  1、监听指定目录文件/home/<USER>/home/<USER>

  9、SUB高轨发送的文件，保存到文件的绝对路径下geo_data-localhost:13239

  0、SUB电侦发送的文件，保存到文件的绝对路径下leo_data-localhost:13237


发布：
消息主题，文件名，文件大小，文件绝对路径，文件内容

接收：
消息主题，文件名，文件大小，文件绝对路径，文件内容

可见光-红外-全色-Sar
  (数据源：图像+json)卫星id_0_时间戳.jpg/json
  (中间结果：json)
    14种算法：卫星id_BHSF_时间戳.json
    位置转换：卫星id_WZZH_时间戳.json
    
电侦(数据源：npy+json)(中间结果：图像+npy)
雷达(数据源：json+json)(中间结果：json)