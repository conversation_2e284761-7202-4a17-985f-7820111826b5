# Frame 数据接收系统配置文件
# 修改此文件后需要重启frame服务才能生效

[frame]
# 监控目录
watch_dirs = /home/<USER>/home/<USER>
save_dir = /home/<USER>
# 文件命名策略：original, timestamp, custom
naming_strategy = original
# 重复文件处理：overwrite, rename, skip
duplicate_handling = skip
# 是否创建按日期分组的子目录
create_date_dirs = false
# 自定义文件名模板（当naming_strategy=custom时使用）
custom_name_template = {timestamp}_{original_name}
# 数据库文件路径
database_path = ./task_data.db
# 是否插入示例数据
insert_sample_data = false
# 是否启用任务数据解析和存储
enable_task_data_parsing = true

[api]
# 是否启用API服务
enable_api = true
# API服务主机地址
host = 0.0.0.0
# API服务端口
port = 13231
# API服务标题
title = Frame Task Data API
# API服务描述
description = 提供任务数据的RESTful API接口
# API版本
version = 1.0.0
# 是否启用API文档
enable_docs = true
# 允许的跨域来源（多个用逗号分隔，*表示允许所有）
cors_origins = *
# 是否允许跨域凭证
cors_credentials = true
# 允许的HTTP方法
cors_methods = GET,POST,PUT,DELETE,OPTIONS
# 允许的HTTP头
cors_headers = *

[zmq]
# 是否启用ZeroMQ发布端
enable_zmq = true
# ZeroMQ绑定地址
bind_address = tcp://0.0.0.0:13237
# 发布主题
topic = file_data
# 保存文件目录
result_output_dir = /home/<USER>/api_results
# 是否启用目录监听
enable_directory_watch = false

[logging]
# 日志级别：DEBUG, INFO, WARNING, ERROR, CRITICAL
level = INFO
# 日志格式（注意：INI文件中%需要转义为%%）
format = [%%(asctime)s] [%%(name)s] %%(message)s
# 日志格式（注意：INI文件中%需要转义为%%）
date_format = %%Y-%%m-%%d %%H:%%M:%%S