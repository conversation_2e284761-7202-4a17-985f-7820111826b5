import os
import configparser
import logging
from typing import Dict, Any, Optional


class ConfigManager:
    """配置管理器，负责读取和管理 config.ini 配置文件"""
    
    def __init__(self, config_path: str = "config.ini"):
        """
        初始化配置管理器
        
        :param config_path: 配置文件路径，默认为 config.ini
        """
        self.config_path = config_path
        self.config = configparser.ConfigParser()
        self.logger = logging.getLogger('ConfigManager')
        
        # 加载配置文件
        self.load_config()
    
    def load_config(self) -> None:
        """加载配置文件"""
        if not os.path.exists(self.config_path):
            raise FileNotFoundError(f"配置文件未找到: {self.config_path}")
        
        try:
            self.config.read(self.config_path, encoding='utf-8')
            self.logger.info(f"配置文件加载成功: {self.config_path}")
        except Exception as e:
            raise RuntimeError(f"配置文件加载失败: {e}")
    
    def _get_boolean(self, section: str, key: str, default: bool = False) -> bool:
        """获取布尔值配置项"""
        try:
            return self.config.getboolean(section, key)
        except (configparser.NoSectionError, configparser.NoOptionError, ValueError):
            self.logger.warning(f"配置项 [{section}].{key} 未找到或格式错误，使用默认值: {default}")
            return default
    
    def _get_int(self, section: str, key: str, default: int = 0) -> int:
        """获取整数配置项"""
        try:
            return self.config.getint(section, key)
        except (configparser.NoSectionError, configparser.NoOptionError, ValueError):
            self.logger.warning(f"配置项 [{section}].{key} 未找到或格式错误，使用默认值: {default}")
            return default
    
    def _get_float(self, section: str, key: str, default: float = 0.0) -> float:
        """获取浮点数配置项"""
        try:
            return self.config.getfloat(section, key)
        except (configparser.NoSectionError, configparser.NoOptionError, ValueError):
            self.logger.warning(f"配置项 [{section}].{key} 未找到或格式错误，使用默认值: {default}")
            return default
    
    def _get_string(self, section: str, key: str, default: str = "") -> str:
        """获取字符串配置项"""
        try:
            return self.config.get(section, key)
        except (configparser.NoSectionError, configparser.NoOptionError):
            self.logger.warning(f"配置项 [{section}].{key} 未找到，使用默认值: {default}")
            return default
    
    def get_logging_config(self) -> Dict[str, Any]:
        """获取日志配置"""
        level_str = self._get_string('logging', 'level', 'INFO').upper()
        
        # 将字符串转换为logging级别
        level_map = {
            'DEBUG': logging.DEBUG,
            'INFO': logging.INFO,
            'WARNING': logging.WARNING,
            'ERROR': logging.ERROR,
            'CRITICAL': logging.CRITICAL
        }
        
        level = level_map.get(level_str, logging.INFO)
        
        return {
            'level': level,
            'format': self._get_string('logging', 'format', '[%(asctime)s] [%(name)s] %(message)s'),
            'date_format': self._get_string('logging', 'date_format', '%Y-%m-%d %H:%M:%S')
        }
    
    def get_subscriber_config(self) -> Dict[str, Any]:
        """获取订阅者配置"""
        return {
            'address': self._get_string('subscriber', 'address', 'tcp://localhost:5556'),
            'topic': self._get_string('subscriber', 'topic', 'file_data')
        }
    
    def get_all_config(self) -> Dict[str, Dict[str, Any]]:
        """获取所有配置"""
        return {
            'logging': self.get_logging_config(),
            'subscriber': self.get_subscriber_config()
        }
