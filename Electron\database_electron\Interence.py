#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FastAPI Web API服务
提供跨域访问task_data.db数据库的RESTful接口

作者: ZjdAuthor
创建时间: 2025-01-26
"""
from collections import defaultdict
import math
import os
import sys
import logging
import uuid
import uvicorn
import json
import zmq  # 添加ZeroMQ导入
from typing import Optional, Dict, Any
from datetime import datetime, timedelta

# FastAPI相关导入
from fastapi import FastAPI, HTTPException, Query, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
# 导入现有的数据库管理器和配置管理器
from frame import DatabaseManager
from config_manager import ConfigManager


# 全局变量
app = None
db_manager = None
json_generator = None
logger = None
config = None
zmq_publisher = None  # 添加ZeroMQ发布端实例

class ZeroMQPublisher:
    """ZeroMQ发布端类"""
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.context = None
        self.publisher = None
        self.logger = logging.getLogger('ZeroMQPublisher')
        
    def connect(self) -> bool:
        """连接到ZeroMQ发布端"""
        try:
            # 获取ZeroMQ配置
            zmq_config = self.config.get('zmq', {})
            bind_address = zmq_config.get('bind_address', 'tcp://*:5557')
            topic = zmq_config.get('topic', 'file_data')
            
            # 创建ZeroMQ上下文和发布套接字
            self.context = zmq.Context()
            self.publisher = self.context.socket(zmq.PUB)
            self.publisher.bind(bind_address)
            
            self.logger.info(f"ZeroMQ发布端已绑定到: {bind_address}, 主题: {topic}")
            return True
            
        except Exception as e:
            self.logger.error(f"ZeroMQ发布端连接失败: {e}")
            return False
    
    def publish_file(self, file_path: str, file_data: bytes) -> bool:
        """发布文件数据"""
        try:
            # 获取ZeroMQ配置
            zmq_config = self.config.get('zmq', {})
            topic = zmq_config.get('topic', 'file_data')
            
            # 获取文件信息
            filename = os.path.basename(file_path)
            abs_filepath = os.path.abspath(file_path)
            file_size = len(file_data)
            
            # 构建消息：[主题, 文件名, 文件大小, 文件绝对路径, 文件数据]
            message = [
                topic.encode('utf-8'),
                filename.encode('utf-8'),
                str(file_size).encode('utf-8'),
                abs_filepath.encode('utf-8'),
                file_data
            ]
            
            # 发布消息
            self.publisher.send_multipart(message)
            self.logger.info(f"文件已发布: {filename} ({file_size}字节)")
            return True
            
        except Exception as e:
            self.logger.error(f"发布文件失败: {e}")
            return False
    
    def disconnect(self):
        """断开ZeroMQ连接"""
        if self.publisher:
            self.publisher.close()
            self.publisher = None
        if self.context:
            self.context.term()
            self.context = None
        self.logger.info("ZeroMQ发布端已断开连接")

# JSON文件生成器类
class JSONFileGenerator:
    """JSON文件生成器，严格按task.txt算法输入格式生成"""
    
    def __init__(self, output_directory: str, zmq_publisher: ZeroMQPublisher):
        self.output_directory = output_directory
        self.zmq_publisher = zmq_publisher
        self.logger = logging.getLogger('JSONFileGenerator')
        
        # 确保输出目录存在
        os.makedirs(output_directory, exist_ok=True)
    
    def _generate_algorithm_filename(self, task_id: str, algorithm_code: str) -> str:
        """生成算法文件名: {task_id}_{algorithm_code}_{timestamp}.json"""
        return f"{task_id}_{algorithm_code}_{str(uuid.uuid4())}.json"
    
    def _save_and_publish_file(self, filename: str, data: dict) -> str:
        """保存JSON文件并通过ZeroMQ发布，返回绝对路径"""
        try:
            filepath = os.path.join(self.output_directory, filename)
            # 保存JSON文件
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            abs_path = os.path.abspath(filepath)
            self.logger.info(f"JSON文件已保存: {abs_path}")
            
            # 读取文件内容并发布（文件名与保存一致）
            with open(filepath, 'rb') as f:
                file_data = f.read()
            
            publish_success = self.zmq_publisher.publish_file(filepath, file_data)
            
            if publish_success:
                self.logger.info(f"文件已发布: {os.path.basename(filepath)}")
            else:
                self.logger.error(f"文件发布失败: {os.path.basename(filepath)}")
            
            return abs_path
            
        except Exception as e:
            self.logger.error(f"保存或发布JSON文件失败: {e}")
            raise

class FileEventHandler:
    """文件事件处理器"""
    def __init__(self, zmq_publisher: ZeroMQPublisher):
        self.zmq_publisher = zmq_publisher
        self.logger = logging.getLogger('FileEventHandler')
    
    def on_created(self, file_path: str):
        """处理文件创建事件"""
        try:
            # 读取文件内容
            with open(file_path, 'rb') as f:
                file_data = f.read()
            
            # 发布文件
            if self.zmq_publisher.publish_file(file_path, file_data):
                self.logger.info(f"文件已发布: {file_path}")
            else:
                self.logger.error(f"文件发布失败: {file_path}")
                
        except Exception as e:
            self.logger.error(f"处理文件创建事件失败: {file_path}, 错误: {e}")

class DirectoryWatcher:
    """目录监听器"""
    def __init__(self, zmq_publisher: ZeroMQPublisher, watch_directory: str):
        self.zmq_publisher = zmq_publisher
        self.watch_directory = watch_directory
        self.observer = None
        self.logger = logging.getLogger('DirectoryWatcher')
    
    def start(self) -> bool:
        """启动目录监听"""
        try:
            # 检查目录是否存在
            if not os.path.exists(self.watch_directory):
                self.logger.error(f"监听目录不存在: {self.watch_directory}")
                return False
            
            # 创建观察者
            from watchdog.observers import Observer
            from watchdog.events import FileSystemEventHandler
            
            class EventHandler(FileSystemEventHandler):
                def __init__(self, file_event_handler: FileEventHandler):
                    super().__init__()
                    self.file_event_handler = file_event_handler
                
                def on_created(self, event):
                    if not event.is_directory:
                        self.file_event_handler.on_created(event.src_path)
            
            # 创建文件事件处理器
            file_event_handler = FileEventHandler(self.zmq_publisher)
            
            # 创建事件处理器
            event_handler = EventHandler(file_event_handler)
            
            # 创建观察者并启动
            self.observer = Observer()
            self.observer.schedule(event_handler, self.watch_directory, recursive=False)
            self.observer.start()
            
            self.logger.info(f"目录监听器已启动: {self.watch_directory}")
            return True
            
        except Exception as e:
            self.logger.error(f"启动目录监听失败: {e}")
            return False
    
    def stop(self):
        """停止目录监听"""
        if self.observer:
            self.observer.stop()
            self.observer.join()
            self.observer = None
        self.logger.info("目录监听器已停止")

def get_database_manager() -> DatabaseManager:
    """获取数据库管理器实例"""
    global db_manager
    if db_manager is None:
        raise HTTPException(status_code=500, detail="数据库管理器未初始化")
    return db_manager


def get_zmq_publisher() -> ZeroMQPublisher:
    """获取ZeroMQ发布端实例"""
    global zmq_publisher
    if zmq_publisher is None:
        raise HTTPException(status_code=500, detail="ZeroMQ发布端未初始化")
    return zmq_publisher


def get_json_generator() -> JSONFileGenerator:
    """获取JSON文件生成器实例"""
    global json_generator
    if json_generator is None:
        raise HTTPException(status_code=500, detail="JSON文件生成器未初始化")
    return json_generator


def init_database_manager():
    """初始化数据库管理器"""
    global db_manager, config, logger
    
    frame_config = config['frame']
    db_path = os.path.abspath(frame_config['database_path'])
    
    try:
        db_manager = DatabaseManager(db_path, logger)
        logger.info(f"数据库管理器初始化成功: {db_path}")
    except Exception as e:
        logger.error(f"数据库管理器初始化失败: {e}")
        raise RuntimeError(f"数据库管理器初始化失败: {e}")


def init_zmq_publisher():
    """初始化ZeroMQ发布端"""
    global zmq_publisher, config, logger
    
    try:
        zmq_publisher = ZeroMQPublisher(config)
        if zmq_publisher.connect():
            logger.info("ZeroMQ发布端初始化成功")
        else:
            logger.error("ZeroMQ发布端连接失败")
            zmq_publisher = None
    except Exception as e:
        logger.error(f"ZeroMQ发布端初始化失败: {e}")
        zmq_publisher = None


def init_json_generator():
    """初始化JSON文件生成器"""
    global json_generator, zmq_publisher, config, logger
    
    try:
        if zmq_publisher is None:
            logger.error("JSON文件生成器初始化失败：ZeroMQ发布端未初始化")
            return
        
        # 获取输出目录配置
        frame_config = config.get('zmq', {})
        output_directory = frame_config.get('result_output_dir', './api_results')
        
        json_generator = JSONFileGenerator(output_directory, zmq_publisher)
        logger.info(f"JSON文件生成器初始化成功，输出目录: {output_directory}")
        
    except Exception as e:
        logger.error(f"JSON文件生成器初始化失败: {e}")
        json_generator = None


# 异常处理器
async def http_exception_handler(request, exc):
    """HTTP异常处理器"""
    _ = request  # 忽略未使用的参数
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "success": False,
            "message": exc.detail,
            "data": None
        }
    )


async def general_exception_handler(request, exc):
    """通用异常处理器"""
    _ = request  # 忽略未使用的参数
    logger.error(f"未处理的异常: {exc}")
    return JSONResponse(
        status_code=500,
        content={
            "success": False,
            "message": "内部服务器错误",
            "data": None
        }
    )


def setup_exception_handlers(app: FastAPI):
    """设置异常处理器"""
    app.add_exception_handler(HTTPException, http_exception_handler)
    app.add_exception_handler(Exception, general_exception_handler)


# 创建FastAPI应用实例
app = FastAPI()

# API路由定义
@app.get("/", summary="API根路径")
async def root():
    """API根路径，返回基本信息"""
    return {
        "success": True,
        "message": "Frame Task Data API",
        "data": {
            "version": "1.0.0",
            "description": "提供任务数据的RESTful API接口",
            "endpoints": [
                "/tasks/position/ - 位置转换查询接口",
                "/tasks/fusion/ - 融合定位查询接口", 
                "/tasks/analyse/ - 态势分析查询接口"
            ]
        }
    }

def quaternion_to_euler(w, x, y, z):
    """
    将四元数 (w, x, y, z) 转换为欧拉角 (yaw, pitch, roll)（单位：弧度）
    旋转顺序：Z-Y-X（先偏航 yaw，再俯仰 pitch，后滚转 roll）
    """
    # 计算偏航角 (yaw) - 绕Z轴旋转
    yaw = math.atan2(2 * (z * w + y * x), 1 - 2 * (z * z + y * y))
    
    # 计算俯仰角 (pitch) - 绕Y轴旋转
    sinp = 2 * (x * w + y * z)
    # 处理sinp超出[-1,1]的情况（浮点误差可能导致轻微越界）
    if abs(sinp) >= 1:
        pitch = math.copysign(math.pi / 2, sinp)  # 使用符号确定方向
    else:
        pitch = math.asin(sinp)
    
    # 计算滚转角 (roll) - 绕X轴旋转
    roll = math.atan2(2 * (w * y - x * z), 1 - 2 * (y * y + x * x))
    
    return yaw, pitch, roll  # 返回弧度值

@app.get("/tasks/position/", summary="位置转换查询")
async def get_position_tasks(
    device_id: str = Query(..., description="卫星ID"),
    timestamp: str = Query(..., description="时间戳"),
    db: DatabaseManager = Depends(get_database_manager),
    json_generator: JSONFileGenerator = Depends(get_json_generator)
):
    """
    位置转换查询接口
    
    - **device_id**: 设备ID（必需）
    - **timestamp**: 时间戳（必需）
    """
    try:
        conn = db.get_connection()
        cursor = conn.cursor()
        
        # 1. 从origional_table获取卫星与载荷参数（不依赖target_id）
        origin_query = """
        SELECT sat_j2000_x, sat_j2000_y, sat_j2000_z,
               load_fov_x, load_fov_y, image_pixel_x, image_pixel_y, sat_qbi_x, sat_qbi_y, sat_qbi_z, sat_qbi_w
        FROM origional_table 
        WHERE sat_id = ? AND timestamp = ?
        LIMIT 1
        """

        cursor.execute(origin_query, [device_id, timestamp])
        origin_row = cursor.fetchone()

        if not origin_row:
            logger.warning(f"未找到device_id={device_id}, timestamp={timestamp}的源数据")
            return {
                "success": False,
                "message": "未找到相关源数据",
                "json_file_path": None
            }

        sat_j2000_x = origin_row[0] or 0.0
        sat_j2000_y = origin_row[1] or 0.0  
        sat_j2000_z = origin_row[2] or 0.0
        load_fov_x = origin_row[3] or 0.0
        load_fov_y = origin_row[4] or 0.0
        cam_pixel_x = origin_row[5] or 0.0
        cam_pixel_y = origin_row[6] or 0.0
        sat_qbi_x = origin_row[7] or 0.0
        sat_qbi_y = origin_row[8] or 0.0
        sat_qbi_z = origin_row[9] or 0.0
        sat_qbi_w = origin_row[10] or 0.0

        # 2. 在feature_table中根据sat_id与timestamp查询所有target_id
        id_query = """
        SELECT DISTINCT target_id,target_x,target_y
        FROM feature_table
        WHERE sat_id = ? AND timestamp = ?
        """
        cursor.execute(id_query, [device_id, timestamp])
        id_rows = cursor.fetchall()
        if not id_rows:
            logger.warning(f"未在feature_table找到sat_id={device_id}, timestamp={timestamp}的目标ID")
            return {
                "success": False,
                "message": "未找到相关目标数据",
                "json_file_path": None
            }

        # 3. 构建targets数组
        targets = []
        # 计算24小时计时的秒数(0-86399)，传入timestamp为unixTime（可能是毫秒或秒）
        try:
            ts_int = int(timestamp)
            # 判断是否为毫秒级（> 10^12 大多为毫秒）
            unix_seconds = ts_int // 1000 if ts_int > 10**12 else (ts_int if ts_int >= 0 else 0)
            seconds_of_day = unix_seconds % 86400
        except Exception:
            seconds_of_day = 0
            logger.warning(f"timestamp无法解析为unixTime整数，已将time置为0: {timestamp}")
        for (target_id,target_x,target_y) in id_rows:

            # 四元数转欧拉角（弧度转度）
            yaw_r, pitch_r, roll_r = quaternion_to_euler(sat_qbi_w, sat_qbi_x, sat_qbi_y, sat_qbi_z)
            yaw = math.degrees(yaw_r)
            pitch = math.degrees(pitch_r)
            roll = math.degrees(roll_r)

            # 4. 按照输入文件定义格式组装数据
            target_data = {
                "sat_id": device_id,
                "target_id": target_id,
                "timestamp": str(timestamp),
                "a": 6378.137,  # 地球椭圆模型长半轴（km）
                "b": 6356.752314245,  # 地球椭圆模型短半轴（km）
                "cx": float(cam_pixel_x),  # 图像主点x
                "cy": float(cam_pixel_y),  # 图像主点y
                "fx": float(load_fov_x),     # 载荷参数x
                "fy": float(load_fov_y),     # 载荷参数y
                "pixel_x": int(target_x),    # 特征目标x
                "pixel_y": int(target_y),    # 特征目标y
                "pitch": pitch,
                "roll": roll,
                "yaw": yaw,
                "position": [
                    float(sat_j2000_x),
                    float(sat_j2000_y), 
                    float(sat_j2000_z)
                ],
                "time": seconds_of_day
            }

            targets.append(target_data)

        # 5. 构建完整的JSON数据结构
        json_data = {
            "targets": targets
        }

        # 6. 生成文件名并保存/发布: {device_id}_WZZH_{时间戳}.json
        filename = json_generator._generate_algorithm_filename(str(device_id), "WZZH")

        abs_path = json_generator._save_and_publish_file(filename, json_data)

        logger.info(f"位置转换JSON文件生成成功: {abs_path}, 包含{len(targets)}个目标")

        return {
            "success": True,
            "message": f"位置转换数据生成成功，包含{len(targets)}个目标",
            "file_path": abs_path
        }
        
    except Exception as e:
        logger.error(f"获取位置转换数据失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取位置转换数据失败: {str(e)}")


@app.get("/tasks/fusion/", summary="融合定位查询")
async def get_fusion_tasks(
    device_id: str = Query(..., description="卫星ID"),
    timestamp: str = Query(..., description="时间戳"),
    db: DatabaseManager = Depends(get_database_manager),
    json_generator: JSONFileGenerator = Depends(get_json_generator)
):
    """
    融合定位查询接口
    
    - **device_id**: 卫星ID（必需）
    - **timestamp**: 时间戳（必需）
    """
    try:
        conn = db.get_connection()
        cursor = conn.cursor()

        # 1. 在feature_table根据sat_id与timestamp获取相关的target_id
        id_query = """
        SELECT DISTINCT target_id
        FROM feature_table 
        WHERE sat_id = ? AND timestamp = ?
        """

        cursor.execute(id_query, [device_id, timestamp])
        id_rows = cursor.fetchall()

        if not id_rows:
            logger.warning(f"未在feature_table找到sat_id={device_id}, timestamp={timestamp}的目标数据")
            return {
                "success": False,
                "message": "未找到相关目标数据",
                "json_file_path": None
            }

        # 2. 收集所有target_id
        target_ids = [row[0] for row in id_rows]

        # 3. 为每个target_id，从feature_table获取所有timestamp下的相关字段值（按timestamp排序）
        observations = []

        feature_query = """
        SELECT target_category, target_longitude, target_latitude, timestamp
        FROM feature_table 
        WHERE sat_id = ? AND target_id = ?
        ORDER BY timestamp ASC
        """

        for target_id in target_ids:
            cursor.execute(feature_query, [device_id, target_id])
            feature_rows = cursor.fetchall()

            for feature_row in feature_rows:
                target_category = feature_row[0]
                target_longitude = feature_row[1] or 0.0
                target_latitude = feature_row[2] or 0.0
                feature_timestamp = feature_row[3]

                # 将target_category转换为数值类型（默认1）
                try:
                    target_type = int(target_category) if isinstance(target_category, (int, float)) or (isinstance(target_category, str) and target_category.isdigit()) else 1
                except Exception:
                    target_type = 1

                observation = {
                    "target_id": target_id,
                    "target_type": target_type,
                    "lon": float(target_longitude),
                    "lat": float(target_latitude),
                    "timestamp": int(feature_timestamp) if feature_timestamp else int(timestamp)
                }

                observations.append(observation)

        # 4. 按照task.txt定义构建JSON（仅parameters和observations）
        json_data = {
            "parameters": {
                "process_noise": 0.02,
                "measurement_noise": 0.5,
                "association_threshold": 0.02
            },
            "observations": observations
        }

        # 5. 生成文件名并保存/发布: {device_id}_DXDW_{timestamp}.json
        filename = json_generator._generate_algorithm_filename(str(device_id), "DXDW")

        abs_path = json_generator._save_and_publish_file(filename, json_data)

        logger.info(f"融合定位JSON文件生成成功: {abs_path}, 包含{len(observations)}个观测数据")

        return {
            "success": True,
            "message": f"融合定位数据生成成功，包含{len(observations)}个观测数据，{len(target_ids)}个目标",
            "file_path": abs_path
        }
        
    except Exception as e:
        logger.error(f"获取融合定位数据失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取融合定位数据失败: {str(e)}")

@app.get("/tasks/dzznsf/", summary="电侦智能算法查询")
async def get_fusion_tasks(
    device_id: str = Query(..., description="卫星ID"),
    timestamp: str = Query(..., description="时间戳"),
    db: DatabaseManager = Depends(get_database_manager),
    json_generator: JSONFileGenerator = Depends(get_json_generator)
):
    """
    电侦智能算法查询接口
    
    - **device_id**: 卫星ID（必需）
    - **timestamp**: 时间戳（必需）
    """
    try:
        conn = db.get_connection()
        cursor = conn.cursor()

        # 1. 在feature_table根据sat_id与timestamp获取到npy的文件路径
        id_query = """
        SELECT 
        FROM feature_table 
        WHERE sat_id = ? AND timestamp = ?
        """

        cursor.execute(id_query, [device_id, timestamp])
        id_rows = cursor.fetchall()

        if not id_rows:
            logger.warning(f"未在feature_table找到sat_id={device_id}, timestamp={timestamp}的目标数据")
            return {
                "success": False,
                "message": "未找到相关目标数据",
                "json_file_path": None
            }

        # 2. 收集所有target_id
        target_ids = [row[0] for row in id_rows]

        # 3. 为每个target_id，从feature_table获取所有timestamp下的相关字段值（按timestamp排序）
        observations = []

        feature_query = """
        SELECT target_category, target_longitude, target_latitude, timestamp
        FROM feature_table 
        WHERE sat_id = ? AND target_id = ?
        ORDER BY timestamp ASC
        """

        for target_id in target_ids:
            cursor.execute(feature_query, [device_id, target_id])
            feature_rows = cursor.fetchall()

            for feature_row in feature_rows:
                target_category = feature_row[0]
                target_longitude = feature_row[1] or 0.0
                target_latitude = feature_row[2] or 0.0
                feature_timestamp = feature_row[3]

                # 将target_category转换为数值类型（默认1）
                try:
                    target_type = int(target_category) if isinstance(target_category, (int, float)) or (isinstance(target_category, str) and target_category.isdigit()) else 1
                except Exception:
                    target_type = 1

                observation = {
                    "target_id": target_id,
                    "target_type": target_type,
                    "lon": float(target_longitude),
                    "lat": float(target_latitude),
                    "timestamp": int(feature_timestamp) if feature_timestamp else int(timestamp)
                }

                observations.append(observation)

        # 4. 按照task.txt定义构建JSON（仅parameters和observations）
        json_data = {
            "parameters": {
                "process_noise": 0.02,
                "measurement_noise": 0.5,
                "association_threshold": 0.02
            },
            "observations": observations
        }

        # 5. 生成文件名并保存/发布: {device_id}_DXDW_{timestamp}.json
        filename = json_generator._generate_algorithm_filename(str(device_id), "DXDW")

        abs_path = json_generator._save_and_publish_file(filename, json_data)

        logger.info(f"融合定位JSON文件生成成功: {abs_path}, 包含{len(observations)}个观测数据")

        return {
            "success": True,
            "message": f"融合定位数据生成成功，包含{len(observations)}个观测数据，{len(target_ids)}个目标",
            "file_path": abs_path
        }
        
    except Exception as e:
        logger.error(f"获取融合定位数据失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取融合定位数据失败: {str(e)}")

@app.get("/tasks/intention/", summary="意图研判查询")
async def get_intention_tasks(
    style: str = Query("3", description="样式，默认为3"),
    db: DatabaseManager = Depends(get_database_manager),
    json_generator: JSONFileGenerator = Depends(get_json_generator)
):
    """
    意图研判查询接口
    
    - **style**: 样式，默认为"3"，3代表将3天的数据进行研判，7代表将7天的数据进行研判
    """
    try:
        conn = db.get_connection()
        cursor = conn.cursor()
        
        # 1) 计算时间范围（毫秒）
        try:
            days = int(style)
        except Exception:
            days = 3
        if days not in (3, 7):
            days = 3
        now_ms = int(datetime.now().timestamp() * 1000)
        days_ms = days * 24 * 60 * 60 * 1000
        operate_end = now_ms
        # # 若库中存在更大的时间戳，则将结束时间扩展到该时间点
        # try:
        #     cursor.execute("SELECT MAX(CAST(timestamp AS INTEGER)) FROM truth_table")
        #     row_max = cursor.fetchone()
        #     if row_max and row_max[0] is not None:
        #         max_ts = int(row_max[0])
        #         if max_ts > operate_end:
        #             operate_end = max_ts
        # except Exception:
        #     pass
        operate_begin = operate_end - days_ms

        # 2) 分舰队顺序查询并组装结构
        truth_query_by_fleet = """
        SELECT target_id, target_category, target_longitude, target_latitude, timestamp
        FROM truth_table
        WHERE CAST(timestamp AS INTEGER) BETWEEN ? AND ? AND fleet_number = ?
        ORDER BY CAST(timestamp AS INTEGER) ASC
        """

        def build_fleet_series(fleet_key: str):
            ts_map: Dict[int, Dict[str, dict]] = {}
            records = 0
            cursor.execute(truth_query_by_fleet, [operate_begin, operate_end, fleet_key])
            for target_id, target_category, lon, lat, ts in cursor.fetchall():
                # 时间
                try:
                    ts_i = int(ts)
                except Exception:
                    continue
                # ship_type：支持字符串映射与数字直通
                ship_type = 2
                if target_category is not None:
                    tc = str(target_category).strip()
                    if tc.isdigit() or (tc.startswith('-') and tc[1:].isdigit()):
                        try:
                            ship_type = int(tc)
                        except Exception:
                            ship_type = 2
                    else:
                        tc_lower = tc.lower()
                        # 用户映射: 'ship'->0, 'car'->1, 'pan'->3
                        str_map = { 'ship': 0, 'car': 1, 'pan': 3 }
                        ship_type = str_map.get(tc_lower, 2)
                # 经纬度
                try:
                    lon_v = float(lon)
                    lat_v = float(lat)
                except Exception:
                    continue
                if target_id is None or str(target_id).strip() == "":
                    continue
                snapshot = ts_map.setdefault(ts_i, {})
                snapshot[str(target_id)] = {
                    "ship_type": ship_type,
                    "latitude": lat_v,
                    "longitude": lon_v,
                    "timestamp": ts_i
                }
                records += 1
            # 序列化为按时间排序的数组
            series = [ts_map[t] for t in sorted(ts_map.keys())]
            return series, records

        fleet_01_series, rec_01 = build_fleet_series('FLEET_01')
        fleet_02_series, rec_02 = build_fleet_series('FLEET_02')
        fleet_03_series, rec_03 = build_fleet_series('FLEET_03')
        total_records = rec_01 + rec_02 + rec_03

        json_data = {
            "query_days": str(days),
            "analyse_time": str(operate_begin),
            "FLEET_01": {"time_series_data": fleet_01_series},
            "FLEET_02": {"time_series_data": fleet_02_series},
            "FLEET_03": {"time_series_data": fleet_03_series},
        }

        # 5) 保存并发布
        filename = json_generator._generate_algorithm_filename("FLEET", "TSFX")
        
        abs_path = json_generator._save_and_publish_file(filename, json_data)

        # 统计时间点数量
        time_points_count = len(json_data["FLEET_01"]["time_series_data"]) + \
                            len(json_data["FLEET_02"]["time_series_data"]) + \
                            len(json_data["FLEET_03"]["time_series_data"]) 

        logger.info(f"意图研判JSON生成成功: {abs_path}, 时间点={time_points_count}, 记录数={total_records}")

        return {
            "success": True,
            "message": f"意图研判数据生成成功，包含{time_points_count}个时间点，{total_records}条记录",
            "file_path": abs_path,
        }
        
    except Exception as e:
        logger.error(f"获取意图研判数据失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取意图研判数据失败: {str(e)}")


def setup_app():
    """设置应用配置"""
    global app, config, logger

    # 加载配置，优先使用环境变量 GEO_CONFIG 指定的路径
    env_config_path = os.environ.get('GEO_CONFIG')
    config_path = env_config_path if env_config_path else os.path.join(os.path.dirname(__file__), "config.ini")
    config_manager = ConfigManager(config_path)
    if not config_manager.validate_config():
        raise RuntimeError("配置文件验证失败")

    config = config_manager.get_all_config()
    api_config = config['api']

    # 配置日志
    log_config = config['logging']
    logging.basicConfig(
        level=log_config['level'],
        format=log_config['format'],
        datefmt=log_config['date_format']
    )
    logger = logging.getLogger('FastAPI')

    # 更新FastAPI应用配置
    docs_url = "/docs" if api_config['enable_docs'] else None
    redoc_url = "/redoc" if api_config['enable_docs'] else None

    app.title = api_config['title']
    app.description = api_config['description']
    app.version = api_config['version']
    app.docs_url = docs_url
    app.redoc_url = redoc_url

    # 配置CORS
    if api_config['cors_origins'] != ['*']:
        origins = [origin.strip() for origin in api_config['cors_origins']]
    else:
        origins = ["*"]

    app.add_middleware(
        CORSMiddleware,
        allow_origins=origins,
        allow_credentials=api_config['cors_credentials'],
        allow_methods=api_config['cors_methods'],
        allow_headers=api_config['cors_headers']
    )

    logger.info(f"FastAPI应用配置完成: {api_config['title']} v{api_config['version']}")


def start_api_server():
    """启动API服务器"""
    try:
        # 设置应用配置
        setup_app()

        # 初始化数据库管理器
        init_database_manager()
        
        # 初始化ZeroMQ发布端
        init_zmq_publisher()
        
        # 初始化JSON文件生成器
        init_json_generator()
        
        # 启动目录监听器
        zmq_config = config.get('zmq', {})
        if zmq_config.get('enable_directory_watch', False):
            watch_directory = zmq_config.get('watch_directory', './watch_data')
            # 转换为绝对路径
            watch_directory = os.path.abspath(os.path.join(os.path.dirname(__file__), watch_directory))
            os.makedirs(watch_directory, exist_ok=True)
            
            directory_watcher = DirectoryWatcher(zmq_publisher, watch_directory)
            if directory_watcher.start():
                logger.info(f"目录监听器已启动: {watch_directory}")
            else:
                logger.error("目录监听器启动失败")

        # 设置异常处理器
        setup_exception_handlers(app)

        # 启动服务
        api_config = config['api']
        logger.info(f"启动API服务: http://{api_config['host']}:{api_config['port']}")
        logger.info("API文档地址: http://{}:{}/docs".format(api_config['host'], api_config['port']))
        logger.info("按 Ctrl+C 停止服务...")

        uvicorn.run(
            app,
            host=api_config['host'],
            port=api_config['port'],
            log_level="info"
        )

    except KeyboardInterrupt:
        logger.info("API服务已停止")
    except Exception as e:
        logger.error(f"启动API服务失败: {e}")
        sys.exit(1)


def print_usage():
    """打印使用说明"""
    print("""

    """)


if __name__ == "__main__":
    # 启动API服务
    start_api_server()


