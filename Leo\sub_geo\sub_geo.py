import os
import sys
import time
import threading
import logging
import zmq
"""Prefer unified Leo/config_manager.py by adding parent directory to path"""
_CUR_DIR = os.path.dirname(__file__)
_PARENT_DIR = os.path.abspath(os.path.join(_CUR_DIR, os.pardir))
if _PARENT_DIR not in sys.path:
    sys.path.insert(0, _PARENT_DIR)

from config_manager import ConfigManager
from typing import List, Union


class LeoSubscriber:
    def __init__(self, zmq_addresses: Union[str, List[str]] = None, 
                 topics: Union[str, List[str]] = None, 
                 save_directory: str = None, 
                 config_path: str = "config.ini"):
        """
        Leo ZeroMQ订阅端，接收来自多个地址和多个主题的文件数据

        :param zmq_addresses: ZeroMQ连接地址（可选，字符串或列表，优先使用配置文件）
        :param topics: ZeroMQ订阅主题（可选，字符串或列表，优先使用配置文件）
        :param save_directory: 文件保存目录（可选）
        :param config_path: 配置文件路径（默认: config.ini）
        """
        # 加载配置：优先使用环境变量 LEO_CONFIG
        env_cfg = os.environ.get('LEO_CONFIG')
        cfg_path = env_cfg if env_cfg else config_path
        self.config_manager = ConfigManager(cfg_path)
        config = self.config_manager.get_all_config()

        # 参数优先级：直接传参 > 配置文件 > 默认值
        subscriber_config = config.get('subscriber', {})
        
        # 处理地址
        if zmq_addresses is None:
            self.zmq_addresses = subscriber_config.get('addresses', ['tcp://localhost:5557'])
        else:
            self.zmq_addresses = self._parse_list(zmq_addresses)
        
        # 处理主题
        if topics is None:
            self.topics = subscriber_config.get('topics', ['file_data'])
        else:
            self.topics = self._parse_list(topics)

        self.running = False
        self.context = None
        self.subscriber = None
        self.receive_thread = None

        # 配置日志
        log_config = config.get('logging', {})
        logging.basicConfig(
            level=log_config.get('level', 'INFO'),
            format=log_config.get('format', '%(asctime)s - %(name)s - %(levelname)s - %(message)s'),
            datefmt=log_config.get('date_format', '%Y-%m-%d %H:%M:%S')
        )
        self.logger = logging.getLogger('LeoSubscriber')
        self.logger.info(f"初始化Leo订阅端")
        self.logger.info(f"ZeroMQ连接地址: {self.zmq_addresses}")
        self.logger.info(f"订阅主题: {self.topics}")

    def _parse_list(self, value: Union[str, List[str]]) -> List[str]:
        """将值解析为字符串列表"""
        if value is None:
            return []
        if isinstance(value, (list, tuple, set)):
            return [str(item).strip() for item in value if str(item).strip()]
        
        # 字符串处理
        str_value = str(value).strip()
        if not str_value:
            return []
        
        # 尝试多种分隔符拆分
        for separator in [';', ',', '|']:
            if separator in str_value:
                return [item.strip() for item in str_value.split(separator) if item.strip()]
        
        # 没有分隔符，整个字符串作为单个元素
        return [str_value]

    def _initialize_zmq(self):
        """初始化ZeroMQ订阅者（支持多地址和多主题）"""
        try:
            self.context = zmq.Context()
            self.subscriber = self.context.socket(zmq.SUB)
            
            # 设置高水位标记，防止内存溢出
            self.subscriber.set_hwm(1000)
            
            # 连接到所有配置的地址
            connected_any = False
            for addr in self.zmq_addresses:
                try:
                    self.subscriber.connect(addr)
                    self.logger.info(f"已连接到 ZeroMQ 地址: {addr}")
                    connected_any = True
                except zmq.ZMQError as ce:
                    self.logger.error(f"连接 ZeroMQ 地址失败: {addr}, 错误: {ce}")
            if not connected_any:
                raise RuntimeError("没有成功连接的ZeroMQ地址，请检查配置的subscriber.address")
            
            # 订阅所有主题
            for topic in self.topics:
                self.subscriber.setsockopt_string(zmq.SUBSCRIBE, topic)
                self.logger.info(f"已订阅主题: {topic}")
            
            self.logger.info(f"ZeroMQ订阅者已初始化，连接地址: {self.zmq_addresses}，订阅主题: {self.topics}")
        except Exception as e:
            self.logger.error(f"ZeroMQ连接初始化失败: {e}")
            if self.subscriber:
                self.subscriber.close(linger=0)
            if self.context:
                self.context.term()
            raise

    def _receive_messages(self):
        """接收ZeroMQ消息并处理文件数据"""
        self.logger.info("开始接收消息...")
        
        poller = zmq.Poller()
        poller.register(self.subscriber, zmq.POLLIN)
        
        while self.running:
            try:
                # 使用轮询器检查是否有消息，超时时间为100ms
                socks = dict(poller.poll(100))
                if self.subscriber in socks and socks[self.subscriber] == zmq.POLLIN:
                    # 接收消息
                    message = self.subscriber.recv_multipart()
                    self._process_message(message)
            except zmq.ZMQError as e:
                if self.running:  # 只有在运行状态下才记录错误
                    self.logger.error(f"ZeroMQ接收错误: {e}")
                break
            except Exception as e:
                self.logger.error(f"处理消息时发生错误: {e}")
                continue
        
        self.logger.info("停止接收消息")

    def _process_message(self, message):
        """处理接收到的消息"""
        try:
            # 按照发送格式解析: [topic, filename, file_size, file_abs_path, file_data]
            if len(message) < 5:
                self.logger.warning(f"消息格式错误，期望至少5部分，实际收到{len(message)}部分")
                return
            
            # 第一部分是主题
            topic = message[0].decode('utf-8')
            
            # 检查消息是否属于我们订阅的主题
            if topic not in self.topics:
                self.logger.debug(f"收到非订阅主题的消息: {topic}")
                return
                
            filename = message[1].decode('utf-8') 
            file_size = int(message[2].decode('utf-8'))
            file_abs_path = message[3].decode('utf-8')
            file_data = message[4]
            
            self.logger.info(f"收到文件: {filename}, 大小: {file_size} 字节, 主题: {topic}")
            
            # 验证文件大小
            if len(file_data) != file_size:
                self.logger.warning(f"文件大小不匹配，期望: {file_size}, 实际: {len(file_data)}")
            
            # 保存文件到绝对路径
            self._save_file_to_absolute_path(filename, file_abs_path, file_data)
            
        except Exception as e:
            self.logger.error(f"解析消息失败: {e}")

    def _save_file_to_absolute_path(self, filename, file_abs_path, file_data):
        """将文件数据保存到绝对路径"""
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(file_abs_path), exist_ok=True)
            
            # 写入文件
            with open(file_abs_path, 'wb') as f:
                f.write(file_data)
            
            self.logger.info(f"文件已保存到: {file_abs_path}")
            
        except Exception as e:
            self.logger.error(f"保存文件失败 {filename}: {e}")

    def start(self):
        """启动订阅服务"""
        if not self.running:
            try:
                # 初始化ZeroMQ
                self._initialize_zmq()
                
                self.running = True
                
                # 启动接收线程
                self.receive_thread = threading.Thread(target=self._receive_messages, daemon=True)
                self.receive_thread.start()
                
                self.logger.info("Leo订阅服务已启动")
                self.logger.info("按 Ctrl+C 停止服务...")
                
                # 主循环等待
                while self.running and self.receive_thread.is_alive():
                    time.sleep(1)
                
            except KeyboardInterrupt:
                self.logger.info("接收到键盘中断信号")
                self.stop()
            except Exception as e:
                self.logger.error(f"启动过程中发生错误: {e}")
                self.stop()
    
    def stop(self):
        """停止订阅服务"""
        if self.running:
            self.running = False
            
            # 等待接收线程结束
            if self.receive_thread and self.receive_thread.is_alive():
                self.receive_thread.join(timeout=5)
                if self.receive_thread.is_alive():
                    self.logger.warning("接收线程未正常退出")
            
            # 关闭ZeroMQ资源
            if self.subscriber:
                self.subscriber.close(linger=0)
            if self.context:
                self.context.term()
            
            self.logger.info("Leo订阅服务已停止")


def main():
    """主函数"""
    try:
        # 创建订阅者实例，使用配置文件
        subscriber = LeoSubscriber()
        
        # 启动订阅服务
        subscriber.start()
        
    except FileNotFoundError as e:
        print(f"错误: {e}")
        print("请确保 config.ini 配置文件存在于当前目录中")
    except Exception as e:
        print(f"启动失败: {e}")


if __name__ == "__main__":
    main()